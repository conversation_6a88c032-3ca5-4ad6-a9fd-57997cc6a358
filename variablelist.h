﻿#ifndef VARIABLELIST_H
#define VARIABLELIST_H
#include <QxOrm.h>
#include <QString>

class VariableList
{
public:
    long id;                // ID
    QString settingName;    // 配置名称
    QString scope;          // 变量作用域
    QString owned;          // 文件名
    QString type;           // 文件分类
    QString name;           // 变量名称
    QString dataType;       // 数据类型
    long dataTypeID;        // 数据类型ID
    int arrayLength;        // 长度
    QString address;        // 地址
    bool isConstant;        // 是否是常量
    bool isOpc;             // 是否是OPC特有项目
    bool isRetained;        // 是否在保持寄存器中
    QString description;    // 注释
    QString createTime;     // 创建时间
    QString lastModifyTime; // 上一次修改时间
    QString varList;        // 所属变量表
    bool isShow;            // 是否显示
    QString initialValue;   // 初始值
    bool isSelected;        // 是否选中
    int state;              // 变量状态
    int modbusAddress;      // Modbus子站对外地址 范围0-65535
    QString modbusRw;       // Modbus子站对外读写 N=不可读写 R=只读 W=可读写
    int segement;           // 段号
    int offset;             // 偏移量
    int masterNo;           // IO变量主站号
    int slaveNo;            // IO变量子站号
    int slotNo;             // IO变量槽位号
    QString modelNumber;    // IO变量模块号
    int priority;           // IO变量优先级1-5
    bool isRedundancy;      // 是否冗余
    QString StructMainName; // 结构体主名称
    QString StructParentName;//结构体父名称
    QString StructName;     // 结构体属性名称
    QString StructDataType; // 结构体数据类型名称
    int StructTotalBitLength; // 结构体属性bit长度
    int StructParentBitOffset; // 结构体属性bit位偏移
    VariableList() {};
};
QX_REGISTER_HPP_EXPORT_DLL(VariableList, qx::trait::no_base_class_defined, 0)

#endif // VARIABLELIST_H
