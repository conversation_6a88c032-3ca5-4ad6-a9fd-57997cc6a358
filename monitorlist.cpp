﻿#include "monitorlist.h"
#include "QxOrm_Impl.h"

QX_REGISTER_CPP_EXPORT_DLL(MonitorList)

namespace qx
{
template <>
void register_class(QxClass<MonitorList> &t)
{
    t.id(&MonitorList::id, "ID");
    t.data(&MonitorList::settingName, "SettingName");
    t.data(&MonitorList::tableName, "TableName");
    t.data(&MonitorList::varID, "VarID");
    t.data(&MonitorList::monitorName, "MonitorName");
    t.data(&MonitorList::monitoredValue, "MonitoredValue");
    t.data(&MonitorList::displayFormat, "DisplayFormat");
    t.data(&MonitorList::monitoringWithTrigger, "MonitoringWithTrigger");
    t.data(&MonitorList::modifyWithTrigger, "ModifyWithTrigger");
    t.data(&MonitorList::modifyValue, "ModifyValue");
    t.data(&MonitorList::isModify, "IsModify");
    t.data(&MonitorList::description, "Description");
    t.data(&MonitorList::state, "State");
    t.data(&MonitorList::scope, "Scope");
    t.data(&MonitorList::owned, "Owned");
    t.data(&MonitorList::type, "Type");
    t.data(&MonitorList::dataType, "DataType");
    t.data(&MonitorList::dataTypeID, "DataTypeID");
}
}
