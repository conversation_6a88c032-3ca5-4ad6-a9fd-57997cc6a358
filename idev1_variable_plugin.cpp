﻿#include "idev1_variable_plugin.h"
#include "FileObject.h"
#include "variableitem.h"
#include "variablemanage.h"
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <qqml.h>

void IDEV1_VariablePlugin::registerTypes(const char *uri)
{
    // 设置组织信息
    QCoreApplication::setOrganizationName(QStringLiteral("sanxiazhikong")); // 使用 QStringLiteral 宏避免不必要的字符串拷贝
    QCoreApplication::setOrganizationDomain(QStringLiteral("sanxiazhikong.com"));

    // @uri IDEVariable
    qmlRegisterType<VariableItem>(uri, 1, 0, "VariableItem");
    //qmlRegisterType<FileObject>("FileObject", 1, 0, "FileObject");

    QQmlApplicationEngine engine;
    FileObject fileObject;
    engine.rootContext()->setContextProperty("FileObject", &fileObject);

    VariableManage &variableManage = VariableManage::instance();
    engine.rootContext()->setContextProperty("VariableManage", &variableManage);

}

