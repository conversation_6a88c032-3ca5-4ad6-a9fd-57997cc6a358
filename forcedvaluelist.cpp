﻿#include "forcedvaluelist.h"
#include "QxOrm_Impl.h"

QX_REGISTER_CPP_EXPORT_DLL(ForcedValueList)

namespace qx
{
template <>
void register_class(QxClass<ForcedValueList> &t)
{
    t.id(&ForcedValueList::id, "ID");
    t.data(&ForcedValueList::settingName, "SettingName");
    t.data(&ForcedValueList::tableName, "TableName");
    t.data(&ForcedValueList::varID, "VarID");
    t.data(&ForcedValueList::forceName, "ForceName");
    t.data(&ForcedValueList::monitoredValue, "MonitoredValue");
    t.data(&ForcedValueList::displayFormat, "DisplayFormat");
    t.data(&ForcedValueList::monitoringWithTrigger, "MonitoringWithTrigger");
    t.data(&ForcedValueList::forcedValue, "ForcedValue");
    t.data(&ForcedValueList::isForced, "IsForced");
    t.data(&ForcedValueList::description, "Description");
    t.data(&ForcedValueList::state, "State");
    t.data(&ForcedValueList::scope, "Scope");
    t.data(&ForcedValueList::owned, "Owned");
    t.data(&ForcedValueList::type, "Type");
    t.data(&ForcedValueList::dataType, "DataType");
    t.data(&ForcedValueList::dataTypeID, "DataTypeID");
}
}
