﻿#ifndef VARIABLETYPE_H
#define VARIABLETYPE_H
#include <QxOrm.h>
#include <QString>

class VariableType
{
public:
    long id;             // ID
    QString settingName; // 配置名称
    QString type;        // 文件分类
    int sortNumber;      // 排序序号
    QString name;        // 名称
    QString shortName;   // 简称
    QString dataType;    // 数据类型
    int arrayCount;      // 数组长度信息
    int deep;            // 层级
    int bitLength;       // 位长度
    long parentID;       // 父节点ID
    long mainID;         // 主节点ID
    QString description; // 注释
    int mainOffset;      // 偏移
    QString iecNORM;     // IEC类型
    QString min;         // 最小值
    QString max;         // 最大值
    QString Initialvalue;         // 默认初始值
    VariableType() {};
};
QX_REGISTER_HPP_EXPORT_DLL(VariableType, qx::trait::no_base_class_defined, 0)

#endif // VARIABLETYPE_H
