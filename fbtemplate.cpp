﻿#include "fbtemplate.h"
#include "QxOrm_Impl.h"

QX_REGISTER_CPP_EXPORT_DLL(FBTemplate)

namespace qx
{
template <>
void register_class(QxClass<FBTemplate> &t)
{
    t.id(&FBTemplate::id, "ID");
    t.data(&FBTemplate::SettingName, "SettingName");
    t.data(&FBTemplate::Owned, "Owned");
    t.data(&FBTemplate::FBType, "FBType");
    t.data(&FBTemplate::ChildType, "ChildType");
    t.data(&FBTemplate::FBName, "FBName");
    t.data(&FBTemplate::Description, "Description");
    t.data(&FBTemplate::Version, "Version");
    t.data(&FBTemplate::CCode, "CCode");
    t.data(&FBTemplate::OFile, "OFile");
    t.data(&FBTemplate::AFile, "AFile");
    t.data(&FBTemplate::SOFile, "SOFile");
    t.data(&FBTemplate::Enable, "Enable");
}
}
