﻿#ifndef FBPINTEMPLATE_H
#define FBPINTEMPLATE_H
#include <QxOrm.h>
#include <QString>


class FBPinTemplate
{
public:
    long id;             // ID
    QString FBName; // 功能名称
    QString Scope;        // 作用域 Input Output InOut Static Local
    QString PinName;      // 引脚名称
    int SortNumber;         //引脚排序
    QString Direction;        // 引脚方向
    QString DataType;   // 引脚数据类型
    QString InitValue;    // 初始值
    QString DefaultValue;      // 默认值
    int ArrayLength;           // 数组长度
    QString Description;       // 注释
    FBPinTemplate() {};
};
QX_REGISTER_HPP_EXPORT_DLL(FBPinTemplate, qx::trait::no_base_class_defined, 0)


#endif // FBPINTEMPLATE_H
