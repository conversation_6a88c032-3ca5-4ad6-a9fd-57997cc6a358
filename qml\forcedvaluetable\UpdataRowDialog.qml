import QtQuick 2.0
import QtQuick.Controls 2.12
import QtQuick.Dialogs 1.3

Dialog {
    id: dialog
    title: "修改行"
    standardButtons: StandardButton.Ok | StandardButton.Cancel

    //使用触发器监视枚举值
    property var selectedOptionText: "永久"

    //强制状态枚举值
    property var selectedStateText: "开启"

    //是否强制枚举值
    property bool selectedForceText: true

    //错误信息
    property string errorMessage

    property int newID
    property int newVarID
    property string newForceName
    property string newMonitoredValue
    property string newDisplayFormat
    property string newMonitoringWithTrigger
    property string newForcedValue
    property bool newIsForced
    property string newDescription
    property string newState

    Column {
        spacing: 10

        Row {
            Text {
                height: 30  // 设置高度
                text: "变量索引："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: varIDInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
                text: newVarID
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "强制表名称："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: forceNameInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
                text: newForceName
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "监视值："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: monitoredValueInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
                text: newMonitoredValue
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "显示格式："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: displayFormatInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
                text: newDisplayFormat
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "使用触发器监视："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            ComboBox {
                id: monitoringWithTriggerInput
                model: ListModel {
                    id: triggerOptionsModel
                    ListElement { text: "永久" }
                    ListElement { text: "暂时" }
                    ListElement { text: "不使用" }
                }
                width: 200  // 设置宽度
                height: 30  // 设置高度
                textRole: "text" // 设置显示文本的角色

                Component.onCompleted: {
                    for (var i = 0; i < triggerOptionsModel.count; i++) {
                        if (triggerOptionsModel.get(i).text === newMonitoringWithTrigger) {
                            currentIndex = i;
                            break;
                        }
                    }
                }

                onActivated: {
                    var selectedIndex = monitoringWithTriggerInput.currentIndex; // 获取当前选中项的索引
                    selectedOptionText = triggerOptionsModel.get(selectedIndex).text; // 获取选中项的文本值
                }
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "强制值："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: forcedValueInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
                text: newForcedValue
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "是否强制："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            ComboBox {
                id: isForcedInput
                model: ListModel {
                    id: forceModel
                    ListElement {
                        text: "true"
                    }
                    ListElement {
                        text: "false"
                    }
                }
                width: 200  // 设置宽度
                height: 30  // 设置高度
                textRole: "text" // 设置显示文本的角色

                Component.onCompleted: {
                    for (var i = 0; i < forceModel.count; i++) {
                        if (forceModel.get(i).text === String(newIsForced)) {
                            currentIndex = i;
                            break;
                        }
                    }
                }

                onActivated: {
                    var selectedIndex = isForcedInput.currentIndex; // 获取当前选中项的索引
                    selectedForceText = JSON.parse(forceModel.get(selectedIndex).text); // 获取选中项的文本值
                    //console.log("选中的选项文本值为：" + selectedForceText); // 输出选中的选项文本值
                }
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "强制注释："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: descriptionInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
                text: newDescription
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "强制状态："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            ComboBox {
                id: stateInput
                model: ListModel {
                    id: stateModel
                    ListElement {
                        text: "开启"
                    }
                    ListElement {
                        text: "关闭"
                    }
                }
                width: 200  // 设置宽度
                height: 30  // 设置高度
                textRole: "text" // 设置显示文本的角色
                Component.onCompleted: {
                    for (var i = 0; i < stateModel.count; i++) {
                        if (stateModel.get(i).text === newState) {
                            currentIndex = i;
                            break;
                        }
                    }
                }

                onActivated: {
                    var selectedIndex = stateInput.currentIndex; // 获取当前选中项的索引
                    selectedStateText = stateModel.get(selectedIndex).text; // 获取选中项的文本值
                }
            }
        }
    }

    onAccepted: {
        if(forceNameInput.text === ""){
            errorMessage = "强制表名称不能为空"
            errorDialog.open()
            return
        }
        var newData = {
            "ID": newID,
            "varID": parseInt(varIDInput.text),
            "forceName": forceNameInput.text,
            "monitoredValue": monitoredValueInput.text,
            "displayFormat": displayFormatInput.text,
            "monitoringWithTrigger": selectedOptionText,
            "forcedValue": forcedValueInput.text,
            "isForced": selectedForceText,
            "description": descriptionInput.text,
            "state": selectedStateText
        };
        //修改操作
        updateCode(newData);
    }

    function updateCode(newData) {
        for (var i = 0; i < etablemodel.count; i++) {
            if (etablemodel.get(i).ID === newData.ID) {
                etablemodel.setProperty(i, "varID", parseInt(newData.varID));
                etablemodel.setProperty(i, "forceName", newData.forceName);
                etablemodel.setProperty(i, "monitoredValue", newData.monitoredValue);
                etablemodel.setProperty(i, "displayFormat", newData.displayFormat);
                etablemodel.setProperty(i, "monitoringWithTrigger", newData.monitoringWithTrigger);
                etablemodel.setProperty(i, "forcedValue", newData.forcedValue);
                etablemodel.setProperty(i, "isForced", newData.isForced);
                etablemodel.setProperty(i, "description", newData.description);
                etablemodel.setProperty(i, "state", newData.state);
                break;
            }
        }
    }

    //错误信息提示
    MessageDialog {
        id: errorDialog
        title: "Error"
        text: errorMessage
        visible: false
        standardButtons: StandardButton.Ok | StandardButton.Cancel
    }
}