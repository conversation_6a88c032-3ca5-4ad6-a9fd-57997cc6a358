﻿#ifndef FBTEMPLATE_H
#define FBTEMPLATE_H
#include <QxOrm.h>
#include <QString>


class FBTemplate
{
public:
    long id;             // ID
    QString SettingName; // 设备配置
    QString Owned;        // 所属  INSIDE=内部  文件=用户定义文件
    QString FBType;      // 类型 FUNCTION / FUNCTIONBLOCK
    QString ChildType;        // 子分类
    QString FBName;   // 功能名称
    QString Description;    // 描述
    QString Version;      // 版本
    QString CCode;            // 层级
    QString OFile;       // 位长度
    QString AFile;       // 父节点ID
    QString SOFile;         // 主节点ID
    int Enable;            //是否启用 1=启用 2=启用且隐藏
    FBTemplate() {};
};
QX_REGISTER_HPP_EXPORT_DLL(FBTemplate, qx::trait::no_base_class_defined, 0)


#endif // FBTEMPLATE_H
