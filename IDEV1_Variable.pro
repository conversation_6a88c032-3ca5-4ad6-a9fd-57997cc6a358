VERSION = 2.0.0

TEMPLATE = lib
TARGET = IDEVariable
QT += qml quick sql xml
CONFIG += plugin qmltypes c++17
DEFINES += IDEVARIABLE_LIBRARY
DEFINES += APP_VERSION=\\\"$$VERSION\\\"

TARGET = $$qtLibraryTarget($$TARGET)
uri = IDEVariable

QML_IMPORT_NAME = $$uri
QML_IMPORT_MAJOR_VERSION = 1

# Input
SOURCES += \
        extendfbc.cpp \
        fbpintemplate.cpp \
        fbreferencelist.cpp \
        fbtemplate.cpp \
        fileobject.cpp \
        forcedvaluelist.cpp \
        idev1_variable_plugin.cpp \
        keywordlist.cpp \
        monitorlist.cpp \
        variableitem.cpp \
        variablelist.cpp \
        variablemanage.cpp \
        variabletype.cpp \
        variableversion.cpp

HEADERS += \
        FileObject.h \
        extendfbc.h \
        fbpintemplate.h \
        fbreferencelist.h \
        fbtemplate.h \
        forcedvaluelist.h \
        idev1_variable_global.h \
        idev1_variable_plugin.h \
        keywordlist.h \
        monitorlist.h \
        variableitem.h \
        variablelist.h \
        variablemanage.h \
        variabletype.h \
        variableversion.h

RESOURCES += \
    qml.qrc

DISTFILES = qmldir

#中文
RC_LANG = 0x0004
# 公司名
QMAKE_TARGET_COMPANY = Three Gorges Intelligent Industrial Control Technology
# 产品名称
QMAKE_TARGET_PRODUCT = ReliAUTO Studio
# 详细描述
QMAKE_TARGET_DESCRIPTION = C++ Application Development Framework
# 版权
QMAKE_TARGET_COPYRIGHT = Copyright(C) 2023-2043 Three Gorges Intelligent Industrial Control Technology



!equals(_PRO_FILE_PWD_, $$OUT_PWD) {
    copy_qmldir.target = $$OUT_PWD/qmldir
    copy_qmldir.depends = $$_PRO_FILE_PWD_/qmldir
    copy_qmldir.commands = $(COPY_FILE) "$$replace(copy_qmldir.depends, /, $$QMAKE_DIR_SEP)" "$$replace(copy_qmldir.target, /, $$QMAKE_DIR_SEP)"
    QMAKE_EXTRA_TARGETS += copy_qmldir
    PRE_TARGETDEPS += $$copy_qmldir.target
}

qmldir.files = qmldir
unix {
    installPath = $$[QT_INSTALL_QML]/$$replace(uri, \., /)
    qmldir.path = $$installPath
    target.path = $$installPath
    INSTALLS += target qmldir
}

win32:CONFIG(release, debug | release)
{
    #指定要拷贝的文件目录为工程目录下release目录下的所有dll、lib文件，例如工程目录在D:\QT\Test
    #PWD就为D:/QT/Test，DllFile = D:/QT/Test/release/*.dll
    TargetDll = $$OUT_PWD/release/*.dll
    TargetLib = $$OUT_PWD/release/*.lib
    Targetplu = $$OUT_PWD/plugins.qmltypes
    Targetdir = $$OUT_PWD/qmldir
    Targethead = $$PWD/*.h
    #将输入目录中的"/"替换为"\"
    TargetDll = $$replace(TargetDll, /, \\)
    TargetLib = $$replace(TargetLib, /, \\)
    Targetplu = $$replace(Targetplu, /, \\)
    Targetdir = $$replace(Targetdir, /, \\)
    Targethead = $$replace(Targethead, /, \\)
    #将输出目录中的"/"替换为"\"
    OutputDllDir =  $$OUT_PWD/../ide_v2.0.1/modules/$$uri/
    OutputDllDir = $$replace(OutputDllDir, /, \\)
    OutputRUNDllDir =  $$OUT_PWD/../build-ide_v2.0.1-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputRUNDllDir = $$replace(OutputRUNDllDir, /, \\)
    OutputLibDir =  $$OUT_PWD/../ide_v2.0.1/lib/
    OutputLibDir = $$replace(OutputLibDir, /, \\)
    OutputHeadDir =  $$OUT_PWD/../ide_v2.0.1/include/$$uri/
    OutputHeadDir = $$replace(OutputHeadDir, /, \\)

    Output2DllDir =  $$OUT_PWD/../IDEV1_MainApp/modules/$$uri/
    Output2DllDir = $$replace(Output2DllDir, /, \\)
    Output2RUNDllDir =  $$OUT_PWD/../build-IDEV1_MainApp-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    Output2RUNDllDir = $$replace(Output2RUNDllDir, /, \\)
    Output2LibDir =  $$OUT_PWD/../IDEV1_MainApp/lib/
    Output2LibDir = $$replace(Output2LibDir, /, \\)
    Output2HeadDir =  $$OUT_PWD/../IDEV1_MainApp/include/$$uri/
    Output2HeadDir = $$replace(Output2HeadDir, /, \\)
    //执行copy命令
    QMAKE_POST_LINK += copy /Y $$TargetDll $$OutputDllDir && copy /Y $$TargetDll $$OutputRUNDllDir && copy /Y $$TargetLib $$OutputLibDir && copy /Y $$Targetplu $$OutputDllDir && copy /Y $$Targetdir $$OutputDllDir && copy /Y $$Targethead $$OutputHeadDir && copy /Y $$TargetDll $$Output2DllDir && copy /Y $$TargetDll $$Output2RUNDllDir && copy /Y $$TargetLib $$Output2LibDir && copy /Y $$Targetplu $$Output2DllDir && copy /Y $$Targetdir $$Output2DllDir && copy /Y $$Targethead $$Output2HeadDir
    #QMAKE_POST_LINK += copy /Y $$TargetDll $$Output2DllDir && copy /Y $$TargetDll $$Output2RUNDllDir && copy /Y $$TargetLib $$Output2LibDir && copy /Y $$Targetplu $$Output2DllDir && copy /Y $$Targetdir $$Output2DllDir && copy /Y $$Targethead $$Output2HeadDir
}



unix:!macx|win32: LIBS += -L$$PWD/IDECommon/include/spdlog/lib/ -lspdlog

INCLUDEPATH += $$PWD/IDECommon/include/spdlog/include
DEPENDPATH += $$PWD/IDECommon/include/spdlog/include

# win32:!win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/include/spdlog/lib/spdlog.lib
# else:unix:!macx|win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/include//spdlog/lib/libspdlog.a

DEFINES += _QX_BUILDING_QX_ORM _QX_STATIC_BUILD

unix:!macx|win32: LIBS += -L$$PWD/IDECommon/include/QxOrm/lib/ -lQxOrm

INCLUDEPATH += $$PWD/IDECommon/include/QxOrm/include
DEPENDPATH += $$PWD/IDECommon/include/QxOrm/include

# win32:!win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/include/QxOrm/lib/QxOrm.lib
# else:unix:!macx|win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/include/QxOrm/lib/libQxOrm.a


unix:!macx|win32: LIBS += -L$$PWD/IDECommon/include/QXlsx/lib/ -lQXlsx

INCLUDEPATH += $$PWD/IDECommon/include/QXlsx/include
DEPENDPATH += $$PWD/IDECommon/include/QXlsx/include

win32:!win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/include/QXlsx/lib/QXlsx.lib
else:unix:!macx|win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/include/QXlsx/lib/libQXlsx.a


unix:!macx|win32: LIBS += -L$$PWD/IDECommon/lib/ -lIDECommon

INCLUDEPATH += $$PWD/IDECommon/include
DEPENDPATH += $$PWD/IDECommon/include

win32:!win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/lib/IDECommon.lib
else:unix:!macx|win32-g++: PRE_TARGETDEPS += $$PWD/IDECommon/lib/libIDECommon.a
