﻿#include "variablelist.h"
#include "QxOrm_Impl.h"

QX_REGISTER_CPP_EXPORT_DLL(VariableList)

namespace qx
{
template <>
void register_class(QxClass<VariableList> &t)
{
    t.id(&VariableList::id, "ID");
    t.data(&VariableList::settingName, "SettingName");
    t.data(&VariableList::scope, "Scope");
    t.data(&VariableList::owned, "Owned");
    t.data(&VariableList::type, "Type");
    t.data(&VariableList::name, "Name");
    t.data(&VariableList::dataType, "DataType");
    t.data(&VariableList::dataTypeID, "DataTypeID");
    t.data(&VariableList::arrayLength, "ArrayLength");
    t.data(&VariableList::address, "Address");
    t.data(&VariableList::isConstant, "IsConstant");
    t.data(&VariableList::isOpc, "IsOpc");
    t.data(&VariableList::isRetained, "IsRetained");
    t.data(&VariableList::description, "Description");
    t.data(&VariableList::createTime, "CreateTime");
    t.data(&VariableList::lastModifyTime, "LastModifyTime");
    t.data(&VariableList::varList, "VarList");
    t.data(&VariableList::isShow, "IsShow");
    t.data(&VariableList::initialValue, "InitialValue");
    t.data(&VariableList::isSelected, "IsSelected");
    t.data(&VariableList::state, "State");

    t.data(&VariableList::modbusAddress, "ModbusAddress");
    t.data(&VariableList::modbusRw, "ModbusRW");
    t.data(&VariableList::segement, "Segement");
    t.data(&VariableList::offset, "Offset");
    t.data(&VariableList::masterNo, "MasterNo");
    t.data(&VariableList::slaveNo, "SlaveNo");
    t.data(&VariableList::slotNo, "SlotNo");
    t.data(&VariableList::modelNumber, "ModelNumber");
    t.data(&VariableList::priority, "Priority");
    t.data(&VariableList::isRedundancy, "IsRedundancy");

    t.data(&VariableList::StructMainName, "StructMainName");
    t.data(&VariableList::StructParentName, "StructParentName");
    t.data(&VariableList::StructName, "StructName");
    t.data(&VariableList::StructDataType, "StructDataType");
    t.data(&VariableList::StructTotalBitLength, "StructTotalBitLength");
    t.data(&VariableList::StructParentBitOffset, "StructParentBitOffset");


}
}
