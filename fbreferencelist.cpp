#include "fbreferencelist.h"
#include "QxOrm_Impl.h"

QX_REGISTER_CPP_EXPORT_DLL(FBReferenceList)

namespace qx
{
    template <>
    void register_class(QxClass<FBReferenceList> &t)
    {
        t.id(&FBReferenceList::id, "ID");
        t.data(&FBReferenceList::settingName, "SettingName");
        t.data(&FBReferenceList::scope, "Scope");
        t.data(&FBReferenceList::owned, "Owned");
        t.data(&FBReferenceList::type, "Type");
        t.data(&FBReferenceList::referenceName, "ReferenceName");
        t.data(&FBReferenceList::instanceName, "InstanceName");
        t.data(&FBReferenceList::fbType, "FBType");
        t.data(&FBReferenceList::createTime, "CreateTime");
        t.data(&FBReferenceList::lastModifyTime, "LastModifyTime");
    }
}
