/*
 * @Author: liquan
 * @Date: 2024-03-27 13:57:26
 * @Last Modified by:   liquan
 * @Last Modified time: 2024-03-27 13:57:26
 */

#pragma execution_character_set("utf-8")
#include "variablemanage.h"
#include "extendfbc.h"
#include "forcedvaluelist.h"
#include "hlog.h"
#include "monitorlist.h"
#include "variabletype.h"
#include "xlsxdocument.h"
#include <QDateTime> // 用于初始化随机数种子
#include <QString>
#include <QVector>
#include <stdlib.h> // 用于随机数生成

VariableManage &VariableManage::instance()
{
    static VariableManage m_instance;
    return m_instance;
}

// 初始化
void VariableManage::init(QSqlDatabase &db)
{
    m_db = db;
}

void VariableManage::setAppPath(const QString &appath)
{
    appDir = appath;
}

// 获取所有变量类型表所有type为BASE和EXTEND的节点或者settingName为deviceName的节点
QVector<QStringList> VariableManage::getVariabTypeVec(const QString &deviceName)
{
    QVector<QStringList> vec;
    QList<VariableType> dataTypeList;
    qx_query query;
    // 获取所有变量类型表所有type为BASE的节点或者settingName为deviceName的节点
    query.where("type")
        .isEqualTo("BASE")
        .or_("type")
        .isEqualTo("EXTEND")
        .or_("type")
        .isEqualTo("PACKAGE")
        .or_("settingName")
        .isEqualTo(deviceName)
        .orderAsc("mainID", "MainOffset", "Deep");
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return vec;
    }
    if (!dataTypeList.isEmpty())
    {
        for (auto dy : dataTypeList)
        {
            QStringList list;

            list << QString::number(dy.id) << dy.settingName << dy.type << QString::number(dy.sortNumber) << dy.name
                 << dy.shortName << dy.dataType << QString::number(dy.arrayCount) << QString::number(dy.deep)
                 << QString::number(dy.bitLength) << QString::number(dy.parentID) << QString::number(dy.mainID)
                 << dy.description << QString::number(dy.mainOffset);
            vec.append(list);
        }
    }
    return vec;
}

QJsonArray VariableManage::getVariabTypeJsonArray(const QString &deviceName)
{
    QJsonArray array;
    QList<VariableType> dataTypeList;
    qx_query query;
    // 获取所有变量类型表所有settingName为deviceName的节点
    query.where("settingName").isEqualTo(deviceName).and_("type").isNotEqualTo("OFF");
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!dataTypeList.isEmpty())
    {
        for (auto dy : dataTypeList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["settingName"] = dy.settingName;
            obj["type"] = dy.type;
            obj["sortNumber"] = dy.sortNumber;
            obj["name"] = dy.name;
            obj["shortName"] = dy.shortName;
            obj["dataType"] = dy.dataType;
            obj["arrayCount"] = dy.arrayCount;
            obj["deep"] = dy.deep;
            obj["bitLength"] = dy.bitLength;
            obj["parentID"] = static_cast<int>(dy.parentID);
            obj["mainID"] = static_cast<int>(dy.mainID);
            obj["description"] = dy.description;
            obj["mainOffset"] = dy.mainOffset;
            array.append(obj);
        }
    }
    return array;
}

// 变量类型表
QJsonArray VariableManage::getDataType(const QString &deviceName)
{
    QJsonArray ary;

    QList<VariableType> dataTypeList;
    qx_query query;
    query.where("deep")
        .isEqualTo(0)
        .and_("type")
        .isNotEqualTo("OFF")
        .and_OpenParenthesis("settingName")
        .isEqualTo(deviceName)
        .or_("settingName")
        .isNull()
        .or_("settingName")
        .isEqualTo("")
        .closeParenthesis();
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return ary;
    }
    if (!dataTypeList.isEmpty())
    {
        for (auto dy : dataTypeList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["name"] = dy.name;
            obj["shortname"] = dy.shortName;
            obj["type"] = dy.type;
            obj["sortnumber"] = dy.sortNumber;
            obj["dataType"] = dy.dataType;
            obj["arraycount"] = dy.arrayCount;
            obj["deep"] = dy.deep;
            obj["parentid"] = static_cast<int>(dy.parentID);
            obj["mainid"] = static_cast<int>(dy.mainID);
            obj["bitlength"] = dy.bitLength;
            obj["description"] = dy.description;
            obj["mainOffset"] = dy.mainOffset;

            ary.append(obj);
        }
    }
    return ary;
}

VariableType VariableManage::getDataTypeByID(const long &id)
{
    VariableType dataTypeNode;
    dataTypeNode.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(dataTypeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return VariableType();
    }
    return dataTypeNode;
}

// 根据id获取变量类型表的所有数据
QJsonArray VariableManage::getDataTypeList(const long &id)
{
    VariableType dataTypeNode;
    dataTypeNode.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(dataTypeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QJsonArray();
    }
    QJsonArray array;
    if (dataTypeNode.id != 0)
    {
        QJsonObject obj;
        obj["vid"] = static_cast<int>(dataTypeNode.id);
        obj["name"] = dataTypeNode.name;
        obj["shortname"] = dataTypeNode.shortName;
        obj["type"] = dataTypeNode.type;
        obj["sortnumber"] = dataTypeNode.sortNumber;
        obj["dataType"] = dataTypeNode.dataType;
        obj["arraycount"] = dataTypeNode.arrayCount;
        obj["deep"] = dataTypeNode.deep;
        obj["parentid"] = static_cast<int>(dataTypeNode.parentID);
        obj["mainid"] = static_cast<int>(dataTypeNode.mainID);
        obj["bitlength"] = dataTypeNode.bitLength;
        obj["description"] = dataTypeNode.description;
        obj["mainOffset"] = dataTypeNode.mainOffset;

        array.append(obj);
    }
    return array;
}

// 根据mainID获取变量类型表的所有数据
QJsonArray VariableManage::getDataTypeListByMainID(const long &mainID)
{
    QJsonArray array;

    QList<VariableType> dataTypeList;
    QSqlError daoError =
        qx::dao::fetch_by_query(qx_query().where("mainID").isEqualTo(QVariant::fromValue(mainID)), dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!dataTypeList.isEmpty())
    {
        for (auto dy : dataTypeList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["name"] = dy.name;
            obj["shortname"] = dy.shortName;
            obj["type"] = dy.type;
            obj["sortnumber"] = dy.sortNumber;
            obj["arraycount"] = dy.arrayCount;
            obj["dataType"] = dy.dataType;
            obj["deep"] = dy.deep;
            obj["parentid"] = static_cast<int>(dy.parentID);
            obj["mainid"] = static_cast<int>(dy.mainID);
            obj["bitlength"] = dy.bitLength;
            obj["description"] = dy.description;
            obj["mainOffset"] = dy.mainOffset;

            array.append(obj);
        }
    }
    return array;
}

// 递归查找某个节点的所有儿子和孙子节点 返回某个节点本身和它的子节点 并按 deep主排序和 sortNumber次排序
QJsonArray VariableManage::getDataTypeChildList(const long &mainID)
{
    QJsonArray array;

    QList<VariableType> parentList;
    // 先按照 `deep` 排序，如果 `deep` 相同，再按照 `sortNumber` 排序。
    qx_query query = qx_query().where("MainID").isEqualTo(QVariant::fromValue(mainID)).orderAsc("deep,sortNumber");
    QSqlError daoError = qx::dao::fetch_by_query(query, parentList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!parentList.isEmpty())
    {
        for (auto dy : parentList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["name"] = dy.name;
            obj["shortname"] = dy.shortName;
            obj["type"] = dy.type;
            obj["sortnumber"] = dy.sortNumber;
            obj["dataType"] = dy.dataType;
            obj["arraycount"] = dy.arrayCount;
            obj["deep"] = dy.deep;
            obj["parentid"] = static_cast<int>(dy.parentID);
            obj["mainid"] = static_cast<int>(dy.mainID);
            obj["bitlength"] = dy.bitLength;
            obj["description"] = dy.description;
            obj["mainOffset"] = dy.mainOffset;

            array.append(obj);
        }
    }
    // qInfo() << "[array] : " << array;
    return array;
}

QList<VariableType> VariableManage::getDataTypeChildListByParentID(const long &parentID)
{
    QList<VariableType> parentList;
    // 先按照 `deep` 排序，如果 `deep` 相同，再按照 `sortNumber` 排序。
    qx_query query = qx_query().where("parentID").isEqualTo(QVariant::fromValue(parentID)).orderAsc("deep,sortNumber");
    QSqlError daoError = qx::dao::fetch_by_query(query, parentList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return parentList;
}

VariableType VariableManage::getSTRUCTDataTypeByName(const QString &deviceName, const QString &name)
{
    QList<VariableType> parentList;
    qx_query query = qx_query()
                         .where("settingName")
                         .isEqualTo(deviceName)
                         .and_("type")
                         .isNotEqualTo("OFF")
                         .and_("dataType")
                         .isEqualTo("STRUCT")
                         .and_("name")
                         .isEqualTo(name)
                         .orderAsc("deep,sortNumber");
    QSqlError daoError = qx::dao::fetch_by_query(query, parentList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return parentList.size() == 1 ? parentList[0] : VariableType();
}

bool VariableManage::addMainDataType(const QString &deviceName, const QString &name)
{
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(name))
    {
        LOG_ERROR_DEFAULT("name is invalid");
        return false;
    }
    qx_query query;
    query.where_OpenParenthesis("settingName")
        .isEqualTo(deviceName)
        .or_("type")
        .isEqualTo("BASE")
        .or_("type")
        .isEqualTo("EXTEND")
        .or_("type")
        .isEqualTo("PACKAGE")
        .closeParenthesis()
        .and_("name")
        .isEqualTo(name)
        .and_("deep")
        .isEqualTo(0);
    long count = qx::dao::count<VariableType>(query, &m_db);
    if (count > 0)
    {
        return false;
    }
    VariableType newType;
    newType.settingName = deviceName;
    newType.mainID = 0;
    newType.parentID = 0;
    newType.name = name;
    newType.type = "USER";
    newType.shortName = "";
    newType.dataType = "STRUCT";
    newType.sortNumber = 0;
    newType.arrayCount = 1;
    newType.deep = 0;
    newType.bitLength = 0;
    newType.description = "";
    newType.mainOffset = 0;
    QSqlError daoError = qx::dao::insert(newType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("insert VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 更新自己的主节点ID
    newType.mainID = newType.id;
    daoError = qx::dao::update(newType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update MainID VariableType:" + daoError.text().toStdString());
        return false;
    }

    emit dataTypeChanged(deviceName, "add", newType.name);

    return true;
}

bool VariableManage::addChildDataType(const QString &deviceName, const long &parentID, const QString &name)
{
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(name))
    {
        LOG_ERROR_DEFAULT("name is invalid");
        return false;
    }
    // 查询重名变量
    qx_query query;
    query.where("settingName")
        .isEqualTo(deviceName)
        .and_("name")
        .isEqualTo(name)
        .and_("parentID")
        .isEqualTo(QVariant::fromValue(parentID));
    long count = qx::dao::count<VariableType>(query, &m_db);
    if (count > 0)
    {
        return false;
    }
    // 防止自定义结构交叉引用

    VariableType parentType;
    parentType.id = parentID;
    QSqlError daoError = qx::dao::fetch_by_id(parentType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search Parent VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 计算当前sortNumber
    qx_query sortNumberQuery;
    sortNumberQuery.where("settingName")
        .isEqualTo(deviceName)
        .and_("parentID")
        .isEqualTo(QVariant::fromValue(parentID));
    long sortNumber = qx::dao::count<VariableType>(sortNumberQuery, &m_db);

    VariableType newType;
    newType.settingName = deviceName;
    newType.type = "ATTRIBUTE";
    newType.sortNumber = static_cast<int>(sortNumber) + 1;
    newType.name = name;
    newType.shortName = QString();
    newType.dataType = "BOOL";
    newType.arrayCount = 1;
    newType.deep = parentType.deep + 1;
    newType.bitLength = 8;
    newType.description = QString();
    newType.mainID = parentType.mainID;
    newType.parentID = parentID;
    newType.mainOffset = 0;

    daoError = qx::dao::insert(newType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("insert VariableType:" + daoError.text().toStdString());
        return false;
    }

    // 重算偏移
    modifyMainOffset(parentType.mainID, 0);

    // 打印所有数据
    emit dataTypeChanged(deviceName, "add", newType.name);
    return true;
}
// 修改叶子节点数据类型后，递归修改父节点的bitLength
// 父节点的bitLength = 所有子节点的bitLength之和
// 直到deep等于0为根节点
bool VariableManage::modifyParentBitLength(const long &id)
{
    // qInfo() << __FILE__ << __func__ << __LINE__ << ".id:" << id << "bitLength:" << bitLength;
    VariableType currType;
    currType.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(currType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 当前节点为根节点停止递归 且需要修改其它引用该数据类型的长度和偏移
    if (currType.deep == 0)
    {
        QList<VariableType> otherList;
        qx_query query;
        query.where("settingName")
            .isEqualTo(currType.settingName)
            .and_("type")
            .isEqualTo("ATTRIBUTE")
            .and_("dataType")
            .isEqualTo(currType.name);
        daoError = qx::dao::fetch_by_query(query, otherList, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT("search Other VariableType:" + daoError.text().toStdString());
            return false;
        }
        qDebug() << "otherList" << otherList.size();
        for (auto otherType : otherList)
        {
            // 更新该引用类型的长度
            otherType.bitLength = currType.bitLength * otherType.arrayCount;
            daoError = qx::dao::update(otherType, &m_db);
            if (daoError.type() != QSqlError::NoError)
            {
                LOG_TRACE_DEFAULT("update VariableType:" + daoError.text().toStdString());
                return false;
            }

            // 计算引用类型的父长度
            modifyParentBitLength(otherType.id);

            // 计算偏移
            modifyMainOffset(otherType.mainID, 0);
        }

        return true;
    }
    // 获取父节点
    VariableType parentType;
    parentType.id = currType.parentID;
    daoError = qx::dao::fetch_by_id(parentType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search Parent VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 获取父节点的所有子节点
    QList<VariableType> childList;
    qx_query query;
    query.where("parentID").isEqualTo(QVariant::fromValue(parentType.id));
    daoError = qx::dao::fetch_by_query(query, childList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search Child VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 计算父节点的bitLength,遍历直属子节点的
    int sumBitLength = 0;
    for (auto dy : childList)
    {
        sumBitLength += dy.bitLength;
        // qInfo() << __FILE__ << __func__ << __LINE__ << "dy.id:" << dy.id << "dy.bitLength:" << dy.bitLength;
    }
    // qInfo() << __FILE__ << __func__ << __LINE__ << "parentType.id:" << parentType.id << "sumBitLength:" <<
    // sumBitLength;
    parentType.bitLength = sumBitLength;
    daoError = qx::dao::update(parentType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update Parent VariableType:" + daoError.text().toStdString());
        return false;
    }
    return modifyParentBitLength(parentType.id);
}
// 递归算mainoffset id根节点ID
bool VariableManage::modifyMainOffset(const long &id, const int startindex)
{
    // 获取该节点下每个
    qx_query query;
    query.where("parentID").isEqualTo(QVariant::fromValue(id)).orderAsc("deep", "sortNumber");
    QList<VariableType> dataTypeList;
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }

    qDebug() << "modifyMainOffset dataTypeList" << dataTypeList.size();

    int start = startindex;
    for (auto dt : dataTypeList)
    {
        if (dt.deep != 0)
        {
            dt.mainOffset = start;

            daoError = qx::dao::update(dt, &m_db);
            if (daoError.type() != QSqlError::NoError)
            {
                LOG_TRACE_DEFAULT("update Parent VariableType:" + daoError.text().toStdString());
            }

            if (dt.dataType == "STRUCT" && dt.type == "ATTRIBUTE")
            {
                // 计算其子节点的偏移位
                modifyMainOffset(dt.id, start);
            }

            start = dt.bitLength + start;
        }
    }

    return true;
}

bool VariableManage::get_variable_types(const QString &type, QVector<QSharedPointer<VariableType>> &all_variable_types)
{
    // 清空之前的数据
    all_variable_types.clear();

    // 创建 SQL 查询语句，使用 SELECT *
    QString sql = R"(
            SELECT *
            FROM variabletype
            WHERE type = :type
            GROUP BY mainid
        )";

    // 执行查询
    QSqlQuery query(m_db);
    query.prepare(sql);
    query.bindValue(":type", type);

    // 执行查询并处理结果
    if (!query.exec())
    {
        LOG_ERROR_DEFAULT(query.lastError().text().toStdString());
        return false;
    }

    // 遍历查询结果，将每一行转为 VariableType 对象并加入 QVector
    while (query.next())
    {
        // 创建一个新的 VariableType 对象并填充数据
        QSharedPointer<VariableType> variableType = QSharedPointer<VariableType>::create();

        // 获取查询结果并填充到 VariableType 对象
        variableType->id = query.value("id").toLongLong();
        variableType->settingName = query.value("settingName").toString();
        variableType->type = query.value("type").toString();
        variableType->sortNumber = query.value("sortNumber").toInt();
        variableType->name = query.value("name").toString();
        variableType->shortName = query.value("shortName").toString();
        variableType->dataType = query.value("dataType").toString();
        variableType->arrayCount = query.value("arrayCount").toInt();
        variableType->deep = query.value("deep").toInt();
        variableType->bitLength = query.value("bitLength").toInt();
        variableType->parentID = query.value("parentID").toLongLong();
        variableType->mainID = query.value("mainID").toLongLong();
        variableType->description = query.value("description").toString();
        variableType->mainOffset = query.value("mainOffset").toInt();

        // 将该 VariableType 对象添加到 QVector
        all_variable_types.append(variableType);
    }

    // 如果查询结果为空，返回 false
    if (all_variable_types.isEmpty())
    {
        LOG_ERROR_DEFAULT(query.lastError().text().toStdString());
        return false;
    }

    return true;
}

bool VariableManage::compareVariableType(const QSharedPointer<VariableType> &temp,
                                         const QSharedPointer<VariableType> &targ)
{
    QVector<QSharedPointer<VariableType>> temp_detail;
    QMap<QString, QVariant> map_temp_setting;
    map_temp_setting.insert("MainID", static_cast<int>(temp->mainID));
    if (!get_Table_ByAQMap<QVector<QSharedPointer<VariableType>>>(temp_detail, map_temp_setting))
    {
        LOG_ERROR_DEFAULT(QString("can't get VariableType by mainID:%1").arg(temp->mainID).toStdString());
        return false;
    }

    QVector<QSharedPointer<VariableType>> targ_detail;
    QMap<QString, QVariant> map_targ_setting;
    map_targ_setting.insert("MainID", static_cast<int>(targ->mainID));
    if (!get_Table_ByAQMap<QVector<QSharedPointer<VariableType>>>(targ_detail, map_targ_setting))
    {
        LOG_ERROR_DEFAULT(QString("can't get VariableType by mainID:%1").arg(targ->mainID).toStdString());
        return false;
    }
    if (temp_detail.size() != targ_detail.size())
    {
        return false;
    }
    for (int i = 0; i < temp_detail.size(); i++)
    {
        bool matchFound = true;
        for (int j = 0; j < targ_detail.size(); j++)
        {
            if (temp_detail[i]->name == targ_detail[j]->name && temp_detail[i]->dataType == targ_detail[j]->dataType &&
                temp_detail[i]->arrayCount == targ_detail[j]->arrayCount)
            {
                matchFound = false;
                break;
            }
        }
        if (matchFound)
        {
            return false;
        }
    }
    return true;
}

// 只能修改叶子节点(无子节点的节点)的类型 前端已做判断 只能修改叶子节点
bool VariableManage::modifyChildDataType(const long &id, const QString &type, const QString &name,
                                         const QString &shortName, const int &arraycount, const QString &description)
{
    VariableType currType;
    currType.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(currType, &m_db);
    if (daoError.type() != QSqlError::NoError || currType.deep == 0)
    {
        LOG_TRACE_DEFAULT("search VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 如果修改了名字
    if (name != currType.name)
    {
        // 查询重名变量
        qx_query query;
        query.where("settingName")
            .isEqualTo(currType.settingName)
            .and_("name")
            .isEqualTo(name)
            .and_("parentID")
            .isEqualTo(QVariant::fromValue(currType.parentID));
        long count = qx::dao::count<VariableType>(query, &m_db);
        if (count > 0)
        {
            return false;
        }
    }
    // 查询数据类型是否存在自已引用自己和交叉引用
    VariableType mainType;
    mainType.id = currType.mainID;
    daoError = qx::dao::fetch_by_id(mainType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 自己引用自己
    if (type == mainType.name)
    {
        return false;
    }

    int oldBitLength = currType.bitLength;

    QString desc = description;

    currType.dataType = type;
    currType.bitLength = getBitLengthFromName(currType.settingName, type) * arraycount;
    currType.name = name;
    currType.shortName = shortName;
    currType.arrayCount = arraycount;
    currType.description = desc.replace(",", "，");
    daoError = qx::dao::update(currType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update VariableType:" + daoError.text().toStdString());
        return false;
    }
    // qInfo() << __FILE__ << __func__ << __LINE__ << "oldBitLength:" << oldBitLength << " currType.bitLength:" <<
    // currType.bitLength;
    //  判断修改了类型或者arraycount的bitLength是否与原来相等
    if (oldBitLength != currType.bitLength)
    {
        // 不相等则逐层更新父节点的bitLength
        // qInfo() << __FILE__ << __func__ << __LINE__ << "currType.id:" << currType.id << "currType.bitLength:" <<
        // currType.bitLength;
        if (!modifyParentBitLength(currType.id))
        {
            return false;
        }
    }
    // qInfo() << "getVariabTypeJsonArray" << getVariabTypeJsonArray(currType.settingName);

    modifyMainOffset(currType.mainID, 0);

    emit dataTypeChanged(currType.settingName, "modify", currType.name);

    return true;
}

bool VariableManage::checkSelfReference(const long &id, const QString &type)
{
    VariableType currType;
    currType.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(currType, &m_db);
    if (daoError.type() != QSqlError::NoError || currType.deep == 0)
    {
        LOG_TRACE_DEFAULT("search VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 查询数据类型是否存在自已引用自己和交叉引用
    VariableType mainType;
    mainType.id = currType.mainID;
    daoError = qx::dao::fetch_by_id(mainType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 自己引用自己
    if (type == mainType.name)
    {
        return false;
    }
    // 获取数据类型
    QList<VariableType> dts;
    qx_query query;
    query.where("settingName")
        .isEqualTo(currType.settingName)
        .and_("type")
        .isEqualTo("USER")
        .and_("name")
        .isEqualTo(type);
    daoError = qx::dao::fetch_by_query(query, dts, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("search VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 检查交叉引用
    if (dts.size() == 1)
    {
        m_tempDataTypeListFromCheckReference.clear();
        // 结构体名称加入
        m_tempDataTypeListFromCheckReference.append(mainType.name);
        if (!checkCrossReference(dts[0].id))
        {
            return false;
        }
    }

    return true;
}

bool VariableManage::checkCrossReference(const long &id)
{
    QList<VariableType> childs;
    qx_query query;
    query.where("mainID").isEqualTo(QVariant::fromValue(id)).and_("type").isEqualTo("ATTRIBUTE");
    QSqlError daoError = qx::dao::fetch_by_query(query, childs, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("search VariableType:" + daoError.text().toStdString());
        return false;
    }
    for (auto ch : childs)
    {
        // 不是基础类型
        if (!m_bitLengthMap.contains(ch.dataType))
        {
            // 判断是否已经出现过
            if (m_tempDataTypeListFromCheckReference.contains(ch.dataType))
            {
                return false;
            }
            else
            {
                QList<VariableType> dts;
                qx_query query;
                query.where("settingName")
                    .isEqualTo(ch.settingName)
                    .and_("type")
                    .isEqualTo("USER")
                    .and_("name")
                    .isEqualTo(ch.dataType);
                daoError = qx::dao::fetch_by_query(query, dts, &m_db);
                if (daoError.type() != QSqlError::NoError)
                {
                    LOG_ERROR_DEFAULT("search VariableType:" + daoError.text().toStdString());
                }
                if (dts.size() == 1)
                {
                    return checkCrossReference(dts[0].id);
                }
            }
        }
    }

    return true;
}

// 根据id删除变量类型表中的数据类型
bool VariableManage::deleteDataType(const long &id)
{
    VariableType currType;
    currType.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(currType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableType:" + daoError.text().toStdString());
        return false;
    }
    // 判断是否有子节点(因为当前没有结构体嵌套，所以不用递归删除子节点的子节点)
    if (hasChild(id))
    {
        // 先删除ParentID都为该节点ID的节点, 再删除该节点
        QList<VariableType> dataTypeList;
        qx_query query;
        query.where("parentID").isEqualTo(QVariant::fromValue(id));
        QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT(daoError.text().toStdString());
            return false;
        }
        if (!dataTypeList.isEmpty())
        {
            for (auto dy : dataTypeList)
            {
                daoError = qx::dao::delete_by_id(dy, &m_db);
                if (daoError.type() != QSqlError::NoError)
                {
                    LOG_TRACE_DEFAULT("delete VariableType:" + daoError.text().toStdString());
                    return false;
                }
            }
        }
    }
    else
    {
        // 先将该叶子节点的bitLength置为0
        if (currType.bitLength != 0)
        {
            currType.bitLength = 0;
            daoError = qx::dao::update(currType, &m_db);
            if (daoError.type() != QSqlError::NoError)
            {
                LOG_TRACE_DEFAULT("update VariableType:" + daoError.text().toStdString());
                return false;
            }
            // 更新父节点的bitLength
            modifyParentBitLength(id);
        }
    }
    // 再删除该叶子节点
    daoError = qx::dao::delete_by_id(currType, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("delete VariableType:" + daoError.text().toStdString());
        return false;
    }

    modifyMainOffset(currType.mainID, 0);

    emit dataTypeChanged(currType.settingName, "delete", currType.name);

    return true;
}

// 根据settingName删除所有相关的数据类型
bool VariableManage::deleteDeviceDataType(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::delete_by_query<VariableType>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete VariableType:" + daoError.text().toStdString());
        return false;
    }
    emit dataTypeChanged(deviceName, "delete", "");
    return true;
}

bool VariableManage::hasChild(const long &id)
{
    // 判断是否有子节点
    qx_query query;
    query.where("parentID").isEqualTo(QVariant::fromValue(id));
    long count = qx::dao::count<VariableType>(query, &m_db);
    if (count > 0)
    {
        return true;
    }
    return false;
}

// 获取变量类型表所有数据
QJsonArray VariableManage::getAllVariableType(const QString &deviceName)
{
    QJsonArray array;
    QList<VariableType> dataTypeList;
    qx_query query;
    query.where("deep")
        .isEqualTo(QVariant::fromValue(0))
        .and_OpenParenthesis("settingName")
        .isEqualTo(QVariant::fromValue(deviceName))
        .or_("settingName")
        .isEqualTo("")
        .or_("settingName")
        .isNull()
        .closeParenthesis();
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!dataTypeList.isEmpty())
    {
        for (auto dy : dataTypeList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["settingName"] = dy.settingName;
            obj["type"] = dy.type;
            obj["sortNumber"] = dy.sortNumber;
            obj["name"] = dy.name;
            obj["shortName"] = dy.shortName;
            obj["dataType"] = dy.dataType;
            obj["arrayCount"] = dy.arrayCount;
            obj["deep"] = dy.deep;
            obj["bitLength"] = dy.bitLength;
            obj["parentID"] = static_cast<int>(dy.parentID);
            obj["mainID"] = static_cast<int>(dy.mainID);
            obj["description"] = dy.description;
            obj["mainOffset"] = dy.mainOffset;
            array.append(obj);
        }
    }
    return array;
}

QJsonArray VariableManage::getVariableType(const QString &deviceName, const QStringList &nameList)
{
    qDebug() << "getVariableType" << deviceName << nameList;
    QJsonArray ary;

    QMap<QString, int> map;
    for (auto name : nameList)
    {
        QMap<QString, int> oneMap = getAllStructTypeName(deviceName, name);
        QMap<QString, int>::iterator itor;
        for (itor = oneMap.begin(); itor != oneMap.end(); ++itor)
        {
            map.insert(itor.key(), itor.value());
        }
    }

    qDebug() << "getVariableType map" << map;
    if (map.size() > 0)
    {
        QMap<QString, int>::iterator itor;
        for (itor = map.begin(); itor != map.end(); ++itor)
        {
            QJsonArray cary = getDataTypeListByMainID(itor.value());
            for (int i = 0; i < cary.size(); i++)
            {
                ary.append(cary.at(i));
            }
        }
    }
    qDebug() << "getVariableType json" << ary;
    return ary;
}

QMap<QString, int> VariableManage::getAllStructTypeName(const QString &deviceName, const QString &name)
{
    QMap<QString, int> map;
    VariableType type = getSTRUCTDataTypeByName(deviceName, name);
    if (type.id > 0)
    {
        map.insert(type.name, type.id);
        QList<VariableType> childList = getDataTypeListFromMainID(type.mainID);
        for (auto &row : childList)
        {
            if (row.type == "ATTRIBUTE" && !isBaseDataType(row.dataType))
            {
                QMap<QString, int> childMap = getAllStructTypeName(deviceName, row.dataType);
                if (childMap.size() > 0)
                {
                    QMap<QString, int>::iterator itor;
                    for (itor = childMap.begin(); itor != childMap.end(); ++itor)
                    {
                        // qDebug() << itor.key() << ":" << itor.value();
                        map.insert(itor.key(), itor.value());
                    }
                }
            }
        }
    }
    return map;
}

QList<VariableType> VariableManage::getDataTypeListFromMainID(const long &mainID)
{
    QList<VariableType> dataTypeList;
    QSqlError daoError = qx::dao::fetch_by_query(
        qx_query().where("mainID").isEqualTo(QVariant::fromValue(mainID)).orderAsc("deep", "sortNumber"), dataTypeList,
        &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }

    return dataTypeList;
}

QStringList VariableManage::getBaseDataType()
{
    QStringList list;
    QList<VariableType> dataTypeList;
    qx_query query;
    query.where("type").isEqualTo("BASE");
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    if (!dataTypeList.isEmpty())
    {
        for (auto dt : dataTypeList)
        {
            list.append(dt.name);
        }
    }

    return list;
}

QList<VariableType> VariableManage::getBaseDataTypeList()
{
    QList<VariableType> dataTypeList;
    qx_query query;
    query.where("type").isEqualTo("BASE");
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return dataTypeList;
}

QStringList VariableManage::getBaseDataTypeNameForExport()
{
    QStringList list;
    QList<VariableType> dataTypeList;
    qx_query query;
    query.where("type").isEqualTo("BASE").and_("dataType").isNotEqualTo("STRUCT");
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    if (!dataTypeList.isEmpty())
    {
        for (auto dt : dataTypeList)
        {
            list.append(dt.name);
        }
    }

    return list;
}

QList<VariableType> VariableManage::getDataTypeFromSettingName(const QString &deviceName)
{
    QList<VariableType> dataTypeList;
    qx_query query;
    query.where("deep")
        .isEqualTo(0)
        .and_OpenParenthesis("settingName")
        .isEqualTo("")
        .or_("settingName")
        .isNull()
        .or_("settingName")
        .isEqualTo(deviceName)
        .closeParenthesis();
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return dataTypeList;
}

QString VariableManage::getDefaultInitValueFromDataType(const QString &dataTypeName)
{
    QString value;
    QList<VariableType> list = getBaseDataTypeList();
    for (auto dy : list)
    {
        if (dy.name == dataTypeName)
        {
            value = dy.Initialvalue;
            break;
        }
    }
    return value;
}

bool VariableManage::isBaseDataType(const QString &daName)
{
    qx_query query;
    query.where("type").isEqualTo("BASE").and_("Name").isEqualTo(daName);
    QList<VariableType> dataTypeList;
    QSqlError daoError = qx::dao::fetch_by_query(query, dataTypeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return dataTypeList.size() == 1;
}

// 获取引用了DateTypeID的所有变量名字
QString VariableManage::getVariableNameByDataTypeID(const long &dataTypeID)
{
    QJsonArray array;
    QList<VariableList> varList;
    qx_query query;
    // 获取引用了DateTypeID的前六个变量
    query.where("dataTypeID").isEqualTo(QVariant::fromValue(dataTypeID)).limit(6);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QString();
    }
    QString str;
    if (!varList.isEmpty())
    {
        if (varList.size() > 5)
        {
            str = varList.at(0).name + "、" + varList.at(1).name + "、" + varList.at(2).name + "、" +
                  varList.at(3).name + "、" + varList.at(4).name + "、" + varList.at(5).name + "...";
        }
        else
        {
            // 拼接变量名 最后一个不加逗号
            for (int i = 0; i < varList.size(); i++)
            {
                if (i == varList.size() - 1)
                {
                    str += varList.at(i).name;
                }
                else
                {
                    str += varList.at(i).name + "、";
                }
            }
        }
    }
    return str;
}

// 导入指定文件中的变量表的数据
QString VariableManage::importVariable(const QString devicename, const QString &filePath)
{
    QXlsx::Document xlsx(filePath);
    if (!xlsx.load())
    {
        return "导入失败！！！";
    }
    QList<VariableList> vs;
    QSet<QString> names;
    QXlsx::CellRange range = xlsx.dimension();
    for (int row = range.firstRow() + 1; row <= range.lastRow(); ++row)
    {
        qx_query query;
        VariableList variable;
        variable.settingName = devicename;
        variable.scope = xlsx.cellAt(row, 3) ? xlsx.cellAt(row, 3)->value().toString() : "";
        variable.owned = xlsx.cellAt(row, 5) ? xlsx.cellAt(row, 5)->value().toString() : "";
        variable.type = xlsx.cellAt(row, 7) ? xlsx.cellAt(row, 7)->value().toString() : "";
        variable.name = xlsx.cellAt(row, 9) ? xlsx.cellAt(row, 9)->value().toString() : "";
        variable.dataType = xlsx.cellAt(row, 11) ? xlsx.cellAt(row, 11)->value().toString() : "";
        variable.dataTypeID = xlsx.cellAt(row, 13) ? xlsx.cellAt(row, 13)->value().toLongLong() : 0;
        variable.arrayLength = xlsx.cellAt(row, 15) ? xlsx.cellAt(row, 15)->value().toInt() : 0;
        variable.address = xlsx.cellAt(row, 17) ? xlsx.cellAt(row, 17)->value().toString() : "";
        variable.isConstant = xlsx.cellAt(row, 19) ? xlsx.cellAt(row, 19)->value().toBool() : false;
        variable.isOpc = xlsx.cellAt(row, 21) ? xlsx.cellAt(row, 21)->value().toBool() : false;
        variable.isRetained = xlsx.cellAt(row, 23) ? xlsx.cellAt(row, 23)->value().toBool() : false;
        variable.description = xlsx.cellAt(row, 25) ? xlsx.cellAt(row, 25)->value().toString() : "";
        variable.createTime = xlsx.cellAt(row, 27) ? xlsx.cellAt(row, 27)->value().toString() : "";
        variable.lastModifyTime = xlsx.cellAt(row, 29) ? xlsx.cellAt(row, 29)->value().toString() : "";
        variable.varList = xlsx.cellAt(row, 31) ? xlsx.cellAt(row, 31)->value().toString() : "";
        variable.isShow = xlsx.cellAt(row, 33) ? xlsx.cellAt(row, 33)->value().toBool() : false;
        variable.initialValue = xlsx.cellAt(row, 35) ? xlsx.cellAt(row, 35)->value().toString() : "";
        variable.isSelected = xlsx.cellAt(row, 37) ? xlsx.cellAt(row, 37)->value().toBool() : false;
        variable.state = xlsx.cellAt(row, 39) ? xlsx.cellAt(row, 39)->value().toInt() : 0;
        variable.modbusAddress = xlsx.cellAt(row, 41) ? xlsx.cellAt(row, 41)->value().toInt() : 0;
        variable.modbusRw = xlsx.cellAt(row, 43) ? xlsx.cellAt(row, 43)->value().toString() : "";
        variable.segement = xlsx.cellAt(row, 45) ? xlsx.cellAt(row, 45)->value().toInt() : 0;
        variable.offset = xlsx.cellAt(row, 47) ? xlsx.cellAt(row, 47)->value().toInt() : 0;
        if (variable.name == "")
        {
            continue;
        }

        query.where("name").isEqualTo(QVariant::fromValue(variable.name));
        long count = qx::dao::count<VariableList>(query, &m_db);

        if (count > 0)
        {
            return "导入失败,变量名称[" + variable.name + "]重复请修改！！！";
        }

        // 变量名只能由1到30个字母字母、数字、下划线组成
        // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
        if (!nameExpr.exactMatch(variable.name))
        {
            LOG_ERROR_DEFAULT(variable.name.toStdString() + " is invalid");
            return QString("变量名称[" + variable.name + "]不规范,变量名称只能由2到30个字母、数字、下划线组成!!!");
        }

        if (names.contains(variable.name))
        {
            return "导入失败,变量名称[" + variable.name + "]重复请修改！！！";
        }

        vs.append(variable);
        names.insert(variable.name);
    }

    for (auto v : vs)
    {
        QSqlError daoError = qx::dao::insert(v, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("insert VariableList:" + daoError.text().toStdString());
            return "导入失败！！！";
        }
        emit dataTypeChanged(v.settingName, "add", v.name);
    }
    return QString();
}

QString VariableManage::importGlibalVariable(const QString devicename, const QString &filePath)
{
    QXlsx::Document xlsx(filePath);
    if (!xlsx.load())
    {
        return "导入失败！！！";
    }
    QList<VariableList> vs;
    QXlsx::CellRange range = xlsx.dimension();
    for (int row = range.firstRow() + 1; row <= range.lastRow(); ++row)
    {
        qx_query query;
        VariableList variable;
        variable.settingName = devicename;
        variable.scope = xlsx.cellAt(row, 3) ? xlsx.cellAt(row, 3)->value().toString() : "";
        variable.owned = xlsx.cellAt(row, 5) ? xlsx.cellAt(row, 5)->value().toString() : "";
        variable.type = xlsx.cellAt(row, 7) ? xlsx.cellAt(row, 7)->value().toString() : "";
        variable.name = xlsx.cellAt(row, 9) ? xlsx.cellAt(row, 9)->value().toString() : "";
        variable.dataType = xlsx.cellAt(row, 11) ? xlsx.cellAt(row, 11)->value().toString() : "";
        variable.dataTypeID = xlsx.cellAt(row, 13) ? xlsx.cellAt(row, 13)->value().toLongLong() : 0;
        variable.arrayLength = xlsx.cellAt(row, 15) ? xlsx.cellAt(row, 15)->value().toInt() : 0;
        variable.address = xlsx.cellAt(row, 17) ? xlsx.cellAt(row, 17)->value().toString() : "";
        variable.isConstant = xlsx.cellAt(row, 19) ? xlsx.cellAt(row, 19)->value().toBool() : false;
        variable.isOpc = xlsx.cellAt(row, 21) ? xlsx.cellAt(row, 21)->value().toBool() : false;
        variable.isRetained = xlsx.cellAt(row, 23) ? xlsx.cellAt(row, 23)->value().toBool() : false;
        variable.description = xlsx.cellAt(row, 25) ? xlsx.cellAt(row, 25)->value().toString() : "";
        variable.createTime = xlsx.cellAt(row, 27) ? xlsx.cellAt(row, 27)->value().toString() : "";
        variable.lastModifyTime = xlsx.cellAt(row, 29) ? xlsx.cellAt(row, 29)->value().toString() : "";
        variable.varList = xlsx.cellAt(row, 31) ? xlsx.cellAt(row, 31)->value().toString() : "";
        variable.isShow = xlsx.cellAt(row, 33) ? xlsx.cellAt(row, 33)->value().toBool() : false;
        variable.initialValue = xlsx.cellAt(row, 35) ? xlsx.cellAt(row, 35)->value().toString() : "";
        variable.isSelected = xlsx.cellAt(row, 37) ? xlsx.cellAt(row, 37)->value().toBool() : false;
        variable.state = xlsx.cellAt(row, 39) ? xlsx.cellAt(row, 39)->value().toInt() : 0;
        variable.modbusAddress = xlsx.cellAt(row, 41) ? xlsx.cellAt(row, 41)->value().toInt() : 0;
        variable.modbusRw = xlsx.cellAt(row, 43) ? xlsx.cellAt(row, 43)->value().toString() : "";
        variable.segement = xlsx.cellAt(row, 45) ? xlsx.cellAt(row, 45)->value().toInt() : 0;
        variable.offset = xlsx.cellAt(row, 47) ? xlsx.cellAt(row, 47)->value().toInt() : 0;
        if (variable.name == "")
        {
            continue;
        }

        // 变量名只能由1到30个字母字母、数字、下划线组成
        // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
        if (!nameExpr.exactMatch(variable.name))
        {
            LOG_ERROR_DEFAULT(variable.name.toStdString() + " is invalid");
            return QString("变量名称[" + variable.name + "]不规范,变量名称只能由2到30个字母、数字、下划线组成!!!");
        }

        vs.append(variable);
    }

    for (auto v : vs)
    {
        QSqlError daoError = qx::dao::insert(v, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("insert VariableList:" + daoError.text().toStdString());
            return "导入失败！！！";
        }
        emit dataTypeChanged(v.settingName, "add", v.name);
    }
    return QString();
}

// 根据id导出变量表中的数据
bool VariableManage::exportVariableByID(const QStringList &ids, const QString &filePath, const QString &fileName)
{
    QList<VariableList> varList;
    qx_query query;
    query.query("where id IN (" + ids.join(",") + ")");
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }

    if (!varList.isEmpty())
    {
        // 制作execl
        QXlsx::Document xlsx;

        // 设置换行操作 获取第一个工作表
        QXlsx::Worksheet *sheet = xlsx.currentWorksheet();

        QXlsx::Format format;
        format.setHorizontalAlignment(QXlsx::Format::AlignHCenter);
        format.setVerticalAlignment(QXlsx::Format::AlignVCenter);
        format.setBorderColor(QColor(Qt::black));
        format.setBorderStyle(QXlsx::Format::BorderThick);
        QFont font;
        font.setPointSize(10); // 设置字体大小为10

        format.setFont(font);

        sheet->write("A1", "配置名称", format);
        sheet->mergeCells("A1:B1", format);

        sheet->write("C1", "变量作用域", format);
        sheet->mergeCells("C1:D1", format);

        sheet->write("E1", "文件名", format);
        sheet->mergeCells("E1:F1", format);

        sheet->write("G1", "文件分类", format);
        sheet->mergeCells("G1:H1", format);

        sheet->write("I1", "变量名称", format);
        sheet->mergeCells("I1:J1", format);

        sheet->write("K1", "数据类型", format);
        sheet->mergeCells("K1:L1", format);

        sheet->write("M1", "数据类型ID", format);
        sheet->mergeCells("M1:N1", format);

        sheet->write("O1", "长度", format);
        sheet->mergeCells("O1:P1", format);

        sheet->write("Q1", "地址", format);
        sheet->mergeCells("Q1:R1", format);

        sheet->write("S1", "是否是常量", format);
        sheet->mergeCells("S1:T1", format);

        sheet->write("U1", "是否是OPC特有项目", format);
        sheet->mergeCells("U1:V1", format);

        sheet->write("W1", "是否保持在寄存器中", format);
        sheet->mergeCells("W1:X1", format);

        sheet->write("Y1", "注释", format);
        sheet->mergeCells("Y1:Z1", format);

        sheet->write("AA1", "创建时间", format);
        sheet->mergeCells("AA1:AB1", format);

        sheet->write("AC1", "上一次修改时间", format);
        sheet->mergeCells("AC1:AD1", format);

        sheet->write("AE1", "所属变量表", format);
        sheet->mergeCells("AE1:AF1", format);

        sheet->write("AG1", "是否显示", format);
        sheet->mergeCells("AG1:AH1", format);

        sheet->write("AI1", "初始值", format);
        sheet->mergeCells("AI1:AJ1", format);

        sheet->write("AK1", "是否选中", format);
        sheet->mergeCells("AK1:AL1", format);

        sheet->write("AM1", "变量状态", format);
        sheet->mergeCells("AM1:AN1", format);

        sheet->write("AO1", "Modbus子站对外地址", format);
        sheet->mergeCells("AO1:AP1", format);

        sheet->write("AQ1", "Modbus子站对外读写", format);
        sheet->mergeCells("AQ1:AR1", format);

        sheet->write("AS1", "段号", format);
        sheet->mergeCells("AS1:AT1", format);

        sheet->write("AU1", "偏移量", format);
        sheet->mergeCells("AU1:AV1", format);

        int nextNumber = 2;
        for (auto dy : varList)
        {
            sheet->write("A" + QString::number(nextNumber), dy.settingName, format);
            sheet->mergeCells("A" + QString::number(nextNumber) + ":B" + QString::number(nextNumber), format);

            sheet->write("C" + QString::number(nextNumber), dy.scope, format);
            sheet->mergeCells("C" + QString::number(nextNumber) + ":D" + QString::number(nextNumber), format);

            sheet->write("E" + QString::number(nextNumber), dy.owned, format);
            sheet->mergeCells("E" + QString::number(nextNumber) + ":F" + QString::number(nextNumber), format);

            sheet->write("G" + QString::number(nextNumber), dy.type, format);
            sheet->mergeCells("G" + QString::number(nextNumber) + ":H" + QString::number(nextNumber), format);

            sheet->write("I" + QString::number(nextNumber), dy.name, format);
            sheet->mergeCells("I" + QString::number(nextNumber) + ":J" + QString::number(nextNumber), format);

            sheet->write("K" + QString::number(nextNumber), dy.dataType, format);
            sheet->mergeCells("K" + QString::number(nextNumber) + ":L" + QString::number(nextNumber), format);

            sheet->write("M" + QString::number(nextNumber), QString::number(dy.dataTypeID), format);
            sheet->mergeCells("M" + QString::number(nextNumber) + ":N" + QString::number(nextNumber), format);

            sheet->write("O" + QString::number(nextNumber), dy.arrayLength, format);
            sheet->mergeCells("O" + QString::number(nextNumber) + ":P" + QString::number(nextNumber), format);

            sheet->write("Q" + QString::number(nextNumber), dy.address, format);
            sheet->mergeCells("Q" + QString::number(nextNumber) + ":R" + QString::number(nextNumber), format);

            sheet->write("S" + QString::number(nextNumber), dy.isConstant, format);
            sheet->mergeCells("S" + QString::number(nextNumber) + ":T" + QString::number(nextNumber), format);

            sheet->write("U" + QString::number(nextNumber), dy.isOpc, format);
            sheet->mergeCells("U" + QString::number(nextNumber) + ":V" + QString::number(nextNumber), format);

            sheet->write("W" + QString::number(nextNumber), dy.isRetained, format);
            sheet->mergeCells("W" + QString::number(nextNumber) + ":X" + QString::number(nextNumber), format);

            sheet->write("Y" + QString::number(nextNumber), dy.description, format);
            sheet->mergeCells("Y" + QString::number(nextNumber) + ":Z" + QString::number(nextNumber), format);

            sheet->write("AA" + QString::number(nextNumber), dy.createTime, format);
            sheet->mergeCells("AA" + QString::number(nextNumber) + ":AB" + QString::number(nextNumber), format);

            sheet->write("AC" + QString::number(nextNumber), dy.lastModifyTime, format);
            sheet->mergeCells("AC" + QString::number(nextNumber) + ":AD" + QString::number(nextNumber), format);

            sheet->write("AE" + QString::number(nextNumber), dy.varList, format);
            sheet->mergeCells("AE" + QString::number(nextNumber) + ":AF" + QString::number(nextNumber), format);

            sheet->write("AG" + QString::number(nextNumber), dy.isShow, format);
            sheet->mergeCells("AG" + QString::number(nextNumber) + ":AH" + QString::number(nextNumber), format);

            sheet->write("AI" + QString::number(nextNumber), dy.initialValue, format);
            sheet->mergeCells("AI" + QString::number(nextNumber) + ":AJ" + QString::number(nextNumber), format);

            sheet->write("AK" + QString::number(nextNumber), dy.isSelected, format);
            sheet->mergeCells("AK" + QString::number(nextNumber) + ":AL" + QString::number(nextNumber), format);

            sheet->write("AM" + QString::number(nextNumber), dy.state, format);
            sheet->mergeCells("AM" + QString::number(nextNumber) + ":AN" + QString::number(nextNumber), format);

            sheet->write("AO" + QString::number(nextNumber), dy.state, format);
            sheet->mergeCells("AO" + QString::number(nextNumber) + ":AP" + QString::number(nextNumber), format);

            sheet->write("AQ" + QString::number(nextNumber), dy.state, format);
            sheet->mergeCells("AQ" + QString::number(nextNumber) + ":AR" + QString::number(nextNumber), format);

            sheet->write("AS" + QString::number(nextNumber), dy.state, format);
            sheet->mergeCells("AS" + QString::number(nextNumber) + ":AT" + QString::number(nextNumber), format);

            sheet->write("AU" + QString::number(nextNumber), dy.state, format);
            sheet->mergeCells("AU" + QString::number(nextNumber) + ":AV" + QString::number(nextNumber), format);

            nextNumber++;
        }

        // 保存Excel文件
        QString file = filePath + "/" + fileName + ".xlsx";
        int i = 1;
        while (QFile::exists(file))
        {
            file = filePath + "/" + fileName + "_(" + QString::number(i) + ").xlsx";
            i++;
        }
        if (!xlsx.saveAs(file))
        {
            qInfo() << __LINE__ << __func__ << "文件未保存";
            return false;
        }
    }
    else
    {
        return false;
    }
    return true;
}

bool VariableManage::exportVariableByIOData(const QJsonArray &IODataList, const QString &filePath)
{
    if (!IODataList.isEmpty())
    {
        // 制作execl
        QXlsx::Document xlsx;

        // 设置换行操作 获取第一个工作表
        QXlsx::Worksheet *sheet = xlsx.currentWorksheet();

        QXlsx::Format format;
        format.setHorizontalAlignment(QXlsx::Format::AlignHCenter);
        format.setVerticalAlignment(QXlsx::Format::AlignVCenter);
        format.setBorderColor(QColor(Qt::black));
        format.setBorderStyle(QXlsx::Format::BorderThick);
        QFont font;
        font.setPointSize(10); // 设置字体大小为10

        format.setFont(font);

        sheet->write("A1", "变量作用域", format);
        sheet->mergeCells("A1:B1", format);

        sheet->write("C1", "文件名", format);
        sheet->mergeCells("C1:D1", format);

        sheet->write("E1", "文件分类", format);
        sheet->mergeCells("E1:F1", format);

        sheet->write("G1", "变量名称", format);
        sheet->mergeCells("G1:H1", format);

        sheet->write("I1", "地址", format);
        sheet->mergeCells("I1:J1", format);

        sheet->write("K1", "注释", format);
        sheet->mergeCells("K1:L1", format);

        sheet->write("M1", "数据类型", format);
        sheet->mergeCells("M1:N1", format);

        sheet->write("O1", "所属变量表", format);
        sheet->mergeCells("O1:P1", format);

        sheet->write("Q1", "数据类型ID", format);
        sheet->mergeCells("Q1:R1", format);

        sheet->write("S1", "长度", format);
        sheet->mergeCells("S1:T1", format);

        sheet->write("U1", "是否是常量", format);
        sheet->mergeCells("U1:V1", format);

        sheet->write("W1", "是否是OPC特有项目", format);
        sheet->mergeCells("W1:X1", format);

        sheet->write("Y1", "是否保持在寄存器中", format);
        sheet->mergeCells("Y1:Z1", format);

        sheet->write("AA1", "创建时间", format);
        sheet->mergeCells("AA1:AB1", format);

        sheet->write("AC1", "上一次修改时间", format);
        sheet->mergeCells("AC1:AD1", format);

        sheet->write("AE1", "初始值", format);
        sheet->mergeCells("AE1:AF1", format);

        sheet->write("AG1", "变量状态", format);
        sheet->mergeCells("AG1:AH1", format);

        int nextNumber = 2;
        foreach (const QJsonValue &value, IODataList)
        {
            QJsonObject obj = value.toObject();
            QString scope = obj["scope"].toString();
            QString owned = obj["owned"].toString();
            QString type = obj["type"].toString();
            QString name = obj["name"].toString();
            QString address = obj["address"].toString();
            QString description = obj["description"].toString();
            QString dataType = obj["dataType"].toString();
            QString varList = obj["varList"].toString();
            QString dataTypeID = obj["dataTypeID"].toString();
            QString arrayLength = obj["arrayLength"].toString();
            QString isConstant = obj["isConstant"].toString();
            QString isOPC = obj["isOPC"].toString();
            QString isRetained = obj["isRetained"].toString();
            QString createTime = obj["createTime"].toString();
            QString lastModifyTime = obj["lastModifyTime"].toString();
            QString initialValue = obj["initialValue"].toString();
            QString status = obj["status"].toString();

            sheet->write("A" + QString::number(nextNumber), scope, format);
            sheet->mergeCells("A" + QString::number(nextNumber) + ":B" + QString::number(nextNumber), format);

            sheet->write("C" + QString::number(nextNumber), owned, format);
            sheet->mergeCells("C" + QString::number(nextNumber) + ":D" + QString::number(nextNumber), format);

            sheet->write("E" + QString::number(nextNumber), type, format);
            sheet->mergeCells("E" + QString::number(nextNumber) + ":F" + QString::number(nextNumber), format);

            sheet->write("G" + QString::number(nextNumber), name, format);
            sheet->mergeCells("G" + QString::number(nextNumber) + ":H" + QString::number(nextNumber), format);

            sheet->write("I" + QString::number(nextNumber), address, format);
            sheet->mergeCells("I" + QString::number(nextNumber) + ":J" + QString::number(nextNumber), format);

            sheet->write("K" + QString::number(nextNumber), description, format);
            sheet->mergeCells("K" + QString::number(nextNumber) + ":L" + QString::number(nextNumber), format);

            sheet->write("M" + QString::number(nextNumber), dataType, format);
            sheet->mergeCells("M" + QString::number(nextNumber) + ":N" + QString::number(nextNumber), format);

            sheet->write("O" + QString::number(nextNumber), varList, format);
            sheet->mergeCells("O" + QString::number(nextNumber) + ":P" + QString::number(nextNumber), format);

            sheet->write("Q" + QString::number(nextNumber), dataTypeID, format);
            sheet->mergeCells("Q" + QString::number(nextNumber) + ":R" + QString::number(nextNumber), format);

            sheet->write("S" + QString::number(nextNumber), arrayLength, format);
            sheet->mergeCells("S" + QString::number(nextNumber) + ":T" + QString::number(nextNumber), format);

            sheet->write("U" + QString::number(nextNumber), isConstant, format);
            sheet->mergeCells("U" + QString::number(nextNumber) + ":V" + QString::number(nextNumber), format);

            sheet->write("W" + QString::number(nextNumber), isOPC, format);
            sheet->mergeCells("W" + QString::number(nextNumber) + ":X" + QString::number(nextNumber), format);

            sheet->write("Y" + QString::number(nextNumber), isRetained, format);
            sheet->mergeCells("Y" + QString::number(nextNumber) + ":Z" + QString::number(nextNumber), format);

            sheet->write("AA" + QString::number(nextNumber), createTime, format);
            sheet->mergeCells("AA" + QString::number(nextNumber) + ":AB" + QString::number(nextNumber), format);

            sheet->write("AC" + QString::number(nextNumber), lastModifyTime, format);
            sheet->mergeCells("AC" + QString::number(nextNumber) + ":AD" + QString::number(nextNumber), format);

            sheet->write("AE" + QString::number(nextNumber), initialValue, format);
            sheet->mergeCells("AE" + QString::number(nextNumber) + ":AF" + QString::number(nextNumber), format);

            sheet->write("AG" + QString::number(nextNumber), status, format);
            sheet->mergeCells("AG" + QString::number(nextNumber) + ":AH" + QString::number(nextNumber), format);
            nextNumber++;
        }

        // 保存Excel文件
        QString file = filePath + "/IO.xlsx";
        int i = 1;
        while (QFile::exists(file))
        {
            file = filePath + "/IO_(" + QString::number(i) + ").xlsx";
            i++;
        }
        if (!xlsx.saveAs(file))
        {
            qInfo() << __LINE__ << __func__ << "文件未保存";
            return false;
        }
    }
    else
    {
        return false;
    }
    return true;
    return true;
}

QJsonArray VariableManage::getAllList(const QString &deviceName, const QStringList &scope, const QStringList &type)
{
    QJsonArray array;
    QList<VariableList> varList;

    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("Scope").in(scope).and_("Type").in(type);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!varList.isEmpty())
    {
        for (auto dy : varList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["scope"] = dy.scope;
            obj["owned"] = dy.owned;
            obj["type"] = dy.type;
            obj["name"] = dy.name;
            obj["dataType"] = dy.dataType;
            obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);
            obj["arrayLength"] = dy.arrayLength;
            obj["address"] = dy.address;
            obj["isConstant"] = dy.isConstant;
            obj["isOpc"] = dy.isOpc;
            obj["isRetained"] = dy.isRetained;
            obj["description"] = dy.description;
            obj["createTime"] = dy.createTime;
            obj["lastModifyTime"] = dy.lastModifyTime;
            obj["varList"] = dy.varList;
            obj["isShow"] = dy.isShow;
            obj["initialValue"] = dy.initialValue;
            obj["isSelected"] = static_cast<bool>(dy.isSelected);
            obj["state"] = static_cast<int>(dy.state);
            obj["modbusAddress"] = static_cast<int>(dy.modbusAddress);
            obj["ModbusRw"] = dy.modbusRw;
            obj["segement"] = static_cast<int>(dy.segement);
            obj["offset"] = static_cast<int>(dy.offset);

            array.append(obj);
        }
    }
    return array;
}

QList<VariableList> VariableManage::getVariableListFromSettingName(const QString &deviceName)
{
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return varList;
}

// 获取根据deviceName变量表的对应的所有数据
QVector<QStringList> VariableManage::getVariableListVec(const QString &deviceName)
{
    QVector<QStringList> vec;
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return vec;
    }
    if (!varList.isEmpty())
    {
        for (auto dy : varList)
        {
            QStringList list;
            list << QString::number(dy.id) << dy.settingName << dy.scope << dy.owned << dy.type << dy.name
                 << dy.dataType << QString::number(dy.dataTypeID) << QString::number(dy.arrayLength) << dy.address
                 << QString::number(dy.isConstant) << QString::number(dy.isOpc) << QString::number(dy.isRetained)
                 << dy.description << dy.createTime << dy.lastModifyTime << dy.varList << QString::number(dy.isShow)
                 << dy.initialValue << QString::number(dy.isSelected) << QString::number(dy.state)
                 << QString::number(dy.modbusAddress) << dy.modbusRw << QString::number(dy.segement)
                 << QString::number(dy.offset) << QString::number(dy.priority) << QString::number(dy.masterNo)
                 << QString::number(dy.slaveNo) << QString::number(dy.slotNo) << dy.modelNumber
                 << QString::number(dy.isRedundancy) << dy.StructMainName << dy.StructParentName << dy.StructName
                 << dy.StructDataType << QString::number(dy.StructTotalBitLength)
                 << QString::number(dy.StructParentBitOffset);
            vec.append(list);
        }
    }
    return vec;
}

QVector<QStringList> VariableManage::getVariableListVecWithUnique(const QString &deviceName)
{
    QVector<QStringList> vec;
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName")
        .isEqualTo(deviceName)
        .and_OpenParenthesis("type")
        .isEqualTo("Global")
        .or_("type")
        .isEqualTo("IO")
        .or_("type")
        .isEqualTo("M")
        .or_OpenParenthesis("scope")
        .isEqualTo("Local")
        .and_("type")
        .isEqualTo("PROGRAM")
        .closeParenthesis()
        .closeParenthesis();
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return vec;
    }
    if (!varList.isEmpty())
    {
        for (auto dy : varList)
        {
            QStringList list;
            list << QString::number(dy.id) << dy.settingName << dy.scope << dy.owned << dy.type << dy.name
                 << dy.dataType << QString::number(dy.dataTypeID) << QString::number(dy.arrayLength) << dy.address
                 << QString::number(dy.isConstant) << QString::number(dy.isOpc) << QString::number(dy.isRetained)
                 << "\"" + dy.description.replace(",", "，") + "\"" << dy.createTime << dy.lastModifyTime << dy.varList
                 << QString::number(dy.isShow) << dy.initialValue << QString::number(dy.isSelected)
                 << QString::number(dy.state) << QString::number(dy.modbusAddress) << dy.modbusRw
                 << QString::number(dy.segement) << QString::number(dy.offset) << QString::number(dy.priority)
                 << QString::number(dy.masterNo) << QString::number(dy.slaveNo) << QString::number(dy.slotNo)
                 << dy.modelNumber << QString::number(dy.isRedundancy) << dy.StructMainName << dy.StructParentName
                 << dy.StructName << dy.StructDataType << QString::number(dy.StructTotalBitLength)
                 << QString::number(dy.StructParentBitOffset);
            vec.append(list);
        }
    }
    return vec;
}

QVector<QStringList> VariableManage::getVariableListVecWithFileReference(const QString &deviceName)
{
    QVector<QStringList> vec;
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName")
        .isEqualTo(deviceName)
        .and_("type")
        .isEqualTo("PROGRAM")
        .and_OpenParenthesis("scope")
        .isEqualTo("Global")
        .or_("scope")
        .isEqualTo("IO")
        .or_("scope")
        .isEqualTo("Local")
        .closeParenthesis();
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return vec;
    }
    qDebug() << "getVariableListVecWithFileReference" << varList.size();
    if (!varList.isEmpty())
    {
        for (auto dy : varList)
        {
            QStringList list;
            list << QString::number(dy.id) << dy.settingName << dy.scope << dy.owned << dy.type << dy.name
                 << dy.dataType << QString::number(dy.dataTypeID) << QString::number(dy.arrayLength) << dy.address
                 << QString::number(dy.isConstant) << QString::number(dy.isOpc) << QString::number(dy.isRetained)
                 << "\"" + dy.description.replace(",", "，") + "\"" << dy.createTime << dy.lastModifyTime << dy.varList
                 << QString::number(dy.isShow) << dy.initialValue << QString::number(dy.isSelected)
                 << QString::number(dy.state) << QString::number(dy.modbusAddress) << dy.modbusRw
                 << QString::number(dy.segement) << QString::number(dy.offset) << QString::number(dy.priority)
                 << QString::number(dy.masterNo) << QString::number(dy.slaveNo) << QString::number(dy.slotNo)
                 << dy.modelNumber << QString::number(dy.isRedundancy) << dy.StructMainName << dy.StructParentName
                 << dy.StructName << dy.StructDataType << QString::number(dy.StructTotalBitLength)
                 << QString::number(dy.StructParentBitOffset);
            vec.append(list);
        }
    }
    return vec;
}

QVector<QStringList> VariableManage::getVariableListVecWithFBDefine(const QString &deviceName, const QString &type,
                                                                    const QString &fbName)
{
    QVector<QStringList> vec;
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("type").isEqualTo(type).and_("owned").like(fbName + ".%");
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return vec;
    }

    if (!varList.isEmpty())
    {
        for (auto dy : varList)
        {
            QStringList list;
            QString initialValue = dy.initialValue;
            // 根据变量的数据类型以及其初始值来补充基础型的初始值
            if (dy.scope != "Static" && dy.scope != "Input" && dy.scope != "Output" && dy.scope != "InOut" &&
                isBaseDataType(dy.dataType) && initialValue == "")
            {
                initialValue = getDefaultInitValueFromDataType(dy.dataType);
            }
            list << QString::number(dy.id) << dy.settingName << dy.scope << dy.owned << fbName << dy.type << dy.name
                 << dy.dataType << initialValue << QString::number(dy.arrayLength);
            vec.append(list);
        }
    }
    return vec;
}

// FBReferenceList 输出 Owned ReferenceName InstanceName
QVector<QStringList> VariableManage::getFBReferenceList(const QString &deviceName)
{
    QVector<QStringList> vec;
    QList<FBReferenceList> fbRefList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::fetch_by_query(query, fbRefList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return vec;
    }
    if (!fbRefList.isEmpty())
    {
        for (auto dy : fbRefList)
        {
            QStringList list;
            list << dy.owned << dy.referenceName << dy.instanceName << dy.settingName << dy.fbType;
            vec.append(list);
        }
    }
    return vec;
}

QList<FBReferenceList> VariableManage::getFBReferenceListForCheckCircular(const QString &deviceName)
{
    QList<FBReferenceList> fbRefList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::fetch_by_query(query, fbRefList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return fbRefList;
}

// deviceName = settingName
QJsonArray VariableManage::getVariableList(const QString &deviceName, const QStringList &owned, const QStringList &type)
{
    QVariantList ownedList;
    for (auto dy : owned)
    {
        ownedList.append(dy);
    }
    QVariantList typeList;
    for (auto dy : type)
    {
        typeList.append(dy);
    }
    qDebug() << "ownedList" << ownedList << typeList;
    QJsonArray array;
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("owned").in(ownedList).and_("type").in(typeList);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!varList.isEmpty())
    {
        for (auto dy : varList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["scope"] = dy.scope;
            obj["owned"] = dy.owned;
            obj["type"] = dy.type;
            obj["name"] = dy.name;
            obj["dataType"] = dy.dataType;
            obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);
            obj["arrayLength"] = dy.arrayLength;
            obj["address"] = dy.address;
            obj["isConstant"] = dy.isConstant;
            obj["isOpc"] = dy.isOpc;
            obj["isRetained"] = dy.isRetained;
            obj["description"] = dy.description;
            obj["createTime"] = dy.createTime;
            obj["lastModifyTime"] = dy.lastModifyTime;
            obj["varList"] = dy.varList;
            obj["isShow"] = dy.isShow;
            obj["initialValue"] = dy.initialValue;
            obj["isSelected"] = static_cast<bool>(dy.isSelected);
            obj["state"] = static_cast<int>(dy.state);
            obj["modbusAddress"] = static_cast<int>(dy.modbusAddress);
            obj["ModbusRw"] = dy.modbusRw;
            obj["segement"] = static_cast<int>(dy.segement);
            obj["offset"] = static_cast<int>(dy.offset);
            array.append(obj);
        }
    }
    return array;
}

QJsonArray VariableManage::getVariableListWithScopeAndOwned(const QString &deviceName, const QStringList &scope,
                                                            const QStringList &owned)
{
    QVariantList ownedList;
    for (auto dy : owned)
    {
        ownedList.append(dy);
    }
    QVariantList scopeList;
    for (auto dy : scope)
    {
        scopeList.append(dy);
    }
    QJsonArray array;
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("owned").in(ownedList).and_("scope").in(scopeList);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!varList.isEmpty())
    {
        for (auto dy : varList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["scope"] = dy.scope;
            obj["owned"] = dy.owned;
            obj["type"] = dy.type;
            obj["name"] = dy.name;
            obj["dataType"] = dy.dataType;
            obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);
            obj["arrayLength"] = dy.arrayLength;
            obj["address"] = dy.address;
            obj["isConstant"] = dy.isConstant;
            obj["isOpc"] = dy.isOpc;
            obj["isRetained"] = dy.isRetained;
            obj["description"] = dy.description;
            obj["createTime"] = dy.createTime;
            obj["lastModifyTime"] = dy.lastModifyTime;
            obj["varList"] = dy.varList;
            obj["isShow"] = dy.isShow;
            obj["initialValue"] = dy.initialValue;
            obj["isSelected"] = static_cast<bool>(dy.isSelected);
            obj["state"] = static_cast<int>(dy.state);
            obj["modbusAddress"] = static_cast<int>(dy.modbusAddress);
            obj["ModbusRw"] = dy.modbusRw;
            obj["segement"] = static_cast<int>(dy.segement);
            obj["offset"] = static_cast<int>(dy.offset);
            array.append(obj);
        }
    }
    return array;
}

QJsonArray VariableManage::getVariableListFromOwned(const QString &deviceName, const QString &owned)
{
    // qDebug() << "getFunctionAndBlockFromFileAndVar" << deviceName << owned;
    QJsonArray ary;
    QList<VariableList> varList;
    qx_query query;
    // qDebug() << "getFunctionAndBlockFromFileAndVar" << "1";
    query.where("settingName").isEqualTo(deviceName).and_("owned").isEqualTo(owned);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    // qDebug() << "getFunctionAndBlockFromFileAndVar" << "2";
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }

    if (!varList.isEmpty())
    {
        for (auto dy : varList)
        {
            QJsonObject obj;
            obj["vid"] = static_cast<int>(dy.id);
            obj["scope"] = dy.scope;
            obj["owned"] = dy.owned;
            obj["type"] = dy.type;
            obj["name"] = dy.name;
            obj["dataType"] = dy.dataType;
            obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);
            obj["arrayLength"] = dy.arrayLength;
            obj["address"] = dy.address;
            obj["isConstant"] = dy.isConstant;
            obj["isOpc"] = dy.isOpc;
            obj["isRetained"] = dy.isRetained;
            obj["description"] = dy.description;
            obj["createTime"] = dy.createTime;
            obj["lastModifyTime"] = dy.lastModifyTime;
            obj["varList"] = dy.varList;
            obj["isShow"] = dy.isShow;
            obj["initialValue"] = dy.initialValue;
            obj["isSelected"] = static_cast<bool>(dy.isSelected);
            obj["state"] = static_cast<int>(dy.state);
            obj["modbusAddress"] = static_cast<int>(dy.modbusAddress);
            obj["ModbusRw"] = dy.modbusRw;
            obj["segement"] = static_cast<int>(dy.segement);
            obj["offset"] = static_cast<int>(dy.offset);

            ary.append(obj);
        }
    }
    // qDebug() << "getFunctionAndBlockFromFileAndVar" << "3";
    return ary;
}

QList<VariableList> VariableManage::getIOMVariableListFromOwned(const QString &deviceName, const QString &owned)
{
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("owned").isEqualTo(owned);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return varList;
}

QList<VariableList> VariableManage::getIOMVariableListFromOwnedAndType(const QString &deviceName, const QString &owned,
                                                                       const QString &type)
{
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("owned").isEqualTo(owned).and_("type").isEqualTo(type);
    QSqlError daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return varList;
}

QJsonObject VariableManage::getVariableFromID(const long &varID)
{
    QJsonObject obj;
    VariableList var;
    var.id = varID;
    QSqlError daoError = qx::dao::fetch_by_id(var, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return obj;
    }
    obj["vid"] = static_cast<int>(var.id);
    obj["scope"] = var.scope;
    obj["owned"] = var.owned;
    obj["type"] = var.type;
    obj["name"] = var.name;
    obj["dataType"] = var.dataType;
    obj["dataTypeID"] = static_cast<int>(var.dataTypeID);
    obj["arrayLength"] = var.arrayLength;
    obj["address"] = var.address;
    obj["isConstant"] = var.isConstant;
    obj["isOpc"] = var.isOpc;
    obj["isRetained"] = var.isRetained;
    obj["description"] = var.description;
    obj["createTime"] = var.createTime;
    obj["lastModifyTime"] = var.lastModifyTime;
    obj["varList"] = var.varList;
    obj["isShow"] = var.isShow;
    obj["initialValue"] = var.initialValue;
    obj["isSelected"] = static_cast<bool>(var.isSelected);
    obj["state"] = static_cast<int>(var.state);
    obj["modbusAddress"] = static_cast<int>(var.modbusAddress);
    obj["ModbusRw"] = var.modbusRw;
    obj["segement"] = static_cast<int>(var.segement);
    obj["offset"] = static_cast<int>(var.offset);
    return obj;
}

// 同设备下 变量名name全局唯一
bool VariableManage::addVariable(const QString &deviceName, const QString &scope, const QString &owned,
                                 const QString &type, const QString &name, const QString &datatype,
                                 const long &dataTypeID, const QString &address)
{
    // 查询重名变量 - 不区分大小写
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QList<VariableList> existingVars;
    QSqlError daoError = qx::dao::fetch_by_query(query, existingVars, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }

    // 在应用层进行不区分大小写的名称比较
    for (const auto &var : existingVars)
    {
        if (QString::compare(var.name, name, Qt::CaseInsensitive) == 0)
        {
            LOG_ERROR_DEFAULT("addVariable count > 0");
            return false;
        }
    }
    // 变量名只能由1到30个字母字母、数字、下划线组成
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(name))
    {
        LOG_ERROR_DEFAULT(name.toStdString() + " is invalid");
        return false;
    }
    VariableList newVar;
    newVar.settingName = deviceName;
    newVar.scope = scope;
    newVar.owned = owned;
    newVar.type = type;
    newVar.name = name;
    newVar.dataType = datatype;
    newVar.dataTypeID = dataTypeID;
    newVar.arrayLength = 1;
    newVar.address = address;
    newVar.isConstant = false;
    newVar.isOpc = false;
    newVar.isRetained = false;
    newVar.description = "";
    newVar.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    newVar.lastModifyTime = newVar.createTime;
    newVar.isShow = true;
    newVar.initialValue = "";
    newVar.isSelected = false;
    newVar.state = 0;
    newVar.modbusAddress = 0;
    newVar.modbusRw = "N";
    newVar.segement = 0;
    newVar.offset = 0;

    newVar.masterNo = -1;
    newVar.slaveNo = -1;
    newVar.slotNo = -1;
    newVar.modelNumber = "";
    newVar.priority = 5;
    newVar.isRedundancy = false;

    daoError = qx::dao::insert(newVar, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("insert VariableList:" + daoError.text().toStdString());
        return false;
    }
    emit variableChanged(newVar.settingName, newVar.owned, "add", newVar.name);
    return true;
}

QString VariableManage::modifyVariable(const long &varID, const QString &scope, const QString &name,
                                       const QString &datatype, const long &dataTypeID, const int &arrayLength,
                                       const QString &address, const QString &initialValue, const bool &isRetained,
                                       QString description)
{
    // 变量名只能由1到30个字母字母、数字、下划线组成
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(name))
    {
        LOG_ERROR_DEFAULT(name.toStdString() + " is invalid");
        return QString("变量名称不规范,变量名称只能由2到30个字母、数字、下划线组成!!!");
    }

    VariableList currVar;
    currVar.id = varID;
    QSqlError daoError = qx::dao::fetch_by_id(currVar, &m_db);

    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoError.text().toStdString());
        return QString("数据错误!!!");
    }

    // 查询所有settingName为currVar.settingName和name为currVar.name的变量
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(currVar.settingName).and_("name").isEqualTo(currVar.name).and_("id").isEqualTo(currVar.id);
    QSqlError daoErrorTemp = qx::dao::fetch_by_query(query, varList, &m_db);

    if (daoErrorTemp.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoErrorTemp.text().toStdString());
        return QString("数据错误!!!");
    }

    // 查询重名变量
    query.clear();
    query.where("settingName").isEqualTo(currVar.settingName);
    QList<VariableList> allVars;
    daoErrorTemp = qx::dao::fetch_by_query(query, allVars, &m_db);

    // 在应用层进行不区分大小写的名称比较
    bool nameExists = false;
    long conflictVarId = -1;
    for (const auto &var : allVars)
    {
        if (QString::compare(var.name, name, Qt::CaseInsensitive) == 0 && var.id != varID)
        {
            nameExists = true;
            conflictVarId = var.id;
            break;
        }
    }

    if (nameExists)
    {
        LOG_ERROR_DEFAULT("addVariable count > 0");
        return QString("变量名称重复!!!");
    }

    //    if (datatype == "BOOL" && arrayLength > 1)
    //    {
    //        return QString("不支持BOOL类型的数组!!!");
    //    }

    currVar.dataType = datatype;
    currVar.arrayLength = arrayLength;
    currVar.address = address;

    if (!checkAddressTrample(currVar))
    {
        return QString("地址发生踩踏!!!");
    }

    for (auto var : varList)
    {
        var.name = name;
        var.scope = scope;
        var.dataType = datatype;
        var.dataTypeID = dataTypeID;
        var.arrayLength = arrayLength;
        var.address = address;
        var.initialValue = initialValue;
        var.isRetained = isRetained;
        var.description = description.replace(",", "，");
        var.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
        daoError = qx::dao::update(var, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT("update VariableList:" + daoError.text().toStdString());
            return QString("修改失败!!!");
        }
    }
    // 发送变量变化信号，包含owned字段信息
    emit variableChanged(currVar.settingName, currVar.owned, "modify", currVar.name);
    return QString();
}

bool VariableManage::checkAddressTrample(const VariableList &currVar)
{
    if (currVar.type != "M")
    {
        return true;
    }

    // 获取数据类型对应的位长度
    int bitLength = getBitLengthFromName(currVar.settingName, currVar.dataType);

    if (bitLength == 0)
    {
        LOG_ERROR_DEFAULT("Unknown data type: " + currVar.dataType.toStdString());
        return false;
    }

    if (bitLength < 8)
    {
        bitLength = 1;
    }
    else
    {
        bitLength = bitLength / 8;
    }

    // modbus起始地址
    double _startModbusAddress = currVar.modbusAddress;
    double _endModbusAddress = _startModbusAddress + bitLength * currVar.arrayLength - 1;

    // 起始地址
    double _startAddress = addressToDouble(currVar.address, currVar.type);
    double _endAddress = _startAddress + bitLength * currVar.arrayLength - 1;

    // 获取当前变量所在设备的相同文件名和文件类型的变量数据
    QList<VariableList> varList = getIOMVariableListFromOwnedAndType(currVar.settingName, currVar.owned, currVar.type);

    // 遍历除开自己的所有变量，校验地址是否踩踏
    for (auto &var : varList)
    {
        if (var.id != currVar.id)
        {
            // 获取数据类型对应的位长度
            bitLength = getBitLengthFromName(var.settingName, var.dataType);

            if (bitLength == 0)
            {
                LOG_ERROR_DEFAULT("Unknown data type: " + var.dataType.toStdString());
                return false;
            }

            if (bitLength < 8)
            {
                bitLength = 1;
            }
            else
            {
                bitLength = bitLength / 8;
            }

            // modbus起始地址
            double startModbusAddress = var.modbusAddress;
            double endModbusAddress = startModbusAddress + bitLength * var.arrayLength - 1;

            // 起始地址
            double startAddress = addressToDouble(var.address, var.type);
            double endAddress = startAddress + bitLength * var.arrayLength - 1;

            // 校验地址是否存在
            if (_startAddress > 0 && _endAddress > 0 && startAddress > 0 && endAddress > 0)
            {
                // 校验地址是否发生踩踏
                if (!(_endAddress < startAddress || endAddress < _startAddress))
                {
                    return false;
                }
            }

            // 校验modbus是否存在对外地址
            if (_startModbusAddress > 0 && _endModbusAddress > 0 && startModbusAddress > 0 && endModbusAddress > 0)
            {
                // 校验modbus地址是否发生踩踏
                if (!(_endModbusAddress < startModbusAddress || endModbusAddress < _startModbusAddress))
                {
                    return false;
                }
            }
        }
    }

    return true;
}

double VariableManage::addressToDouble(const QString &address, const QString &type)
{
    double val = 0;
    // 地址
    QStringList parts = address.split("%" + type);

    for (QString &part : parts)
    {
        if (!part.isEmpty())
        {
            bool ok = false;
            val = part.toDouble(&ok);

            if (ok)
            {
                return val;
            }
        }
    }

    return val;
}

QString VariableManage::modifyVariable(const long &varID, const QString &scope, const QString &name,
                                       const QString &datatype, const long &dataTypeID, const int &arrayLength,
                                       const QString &address, const QString &initialValue, const bool &isRetained,
                                       QString description, const int &modbusAddress, const QString &ModbusRw,
                                       const int &segement, const int &offset)
{
    // 变量名只能由1到30个字母字母、数字、下划线组成
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(name))
    {
        LOG_ERROR_DEFAULT(name.toStdString() + " is invalid");
        return QString("变量名称不规范,变量名称只能由2到30个字母、数字、下划线组成!!!");
    }

    VariableList currVar;
    currVar.id = varID;
    QSqlError daoError = qx::dao::fetch_by_id(currVar, &m_db);

    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoError.text().toStdString());
        return QString("数据错误!!!");
    }

    // 查询所有settingName为currVar.settingName和name为currVar.name的变量
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(currVar.settingName).and_("name").isEqualTo(currVar.name);
    QSqlError daoErrorTemp = qx::dao::fetch_by_query(query, varList, &m_db);

    if (daoErrorTemp.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoErrorTemp.text().toStdString());
        return QString("数据错误!!!");
    }

    // 查询重名变量
    query.clear();
    query.where("settingName").isEqualTo(currVar.settingName);
    QList<VariableList> allVars;
    daoErrorTemp = qx::dao::fetch_by_query(query, allVars, &m_db);

    bool nameExists = false;
    long conflictVarId = -1;
    for (const auto &var : allVars)
    {
        if (QString::compare(var.name, name, Qt::CaseInsensitive) == 0 && var.id != varID)
        {
            nameExists = true;
            conflictVarId = var.id;
            break;
        }
    }

    if (nameExists)
    {
        LOG_ERROR_DEFAULT("addVariable count > 0");
        return QString("变量名称重复!!!");
    }

    currVar.dataType = datatype;
    currVar.arrayLength = arrayLength;
    currVar.address = address;
    currVar.modbusAddress = modbusAddress;

    if (!checkAddressTrample(currVar))
    {
        return QString("地址发生踩踏!!!");
    }

    for (auto var : varList)
    {
        var.name = name;
        var.scope = scope;
        var.dataType = datatype;
        var.dataTypeID = dataTypeID;
        var.arrayLength = arrayLength;
        var.address = address;
        var.initialValue = initialValue;
        var.isRetained = isRetained;
        var.description = description.replace(",", "，");
        var.modbusAddress = modbusAddress;
        var.modbusRw = ModbusRw;
        var.segement = segement;
        var.offset = offset;
        var.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
        daoError = qx::dao::update(var, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT("update VariableList:" + daoError.text().toStdString());
            return QString("修改失败!!!");
        }


    }
    // 发送变量变化信号，包含owned字段信息
    emit variableChanged(currVar.settingName, currVar.owned, "modify", currVar.name);
    return QString();
}

double VariableManage::getNextVariableStartAddress(const QString &deviceName, const QString &owned, const QString &type)
{
    double maxAddress = 0;

    // 获取当前变量所在设备的相同文件名和文件类型的变量数据
    QList<VariableList> varList = getIOMVariableListFromOwnedAndType(deviceName, owned, type);

    for (auto &var : varList)
    {
        // 地址
        QStringList parts = var.address.split("%" + type);

        for (QString &part : parts)
        {
            if (!part.isEmpty())
            {
                bool ok = false;
                double val = part.toDouble(&ok);

                if (ok)
                {
                    // 获取数据类型对应的位长度
                    int bitLength = getBitLengthFromName(var.settingName, var.dataType);

                    if (bitLength == 0)
                    {
                        continue;
                    }

                    if (bitLength < 8)
                    {
                        bitLength = 1;
                    }
                    else
                    {
                        bitLength = bitLength / 8;
                    }

                    // 下一个变量开始地址
                    double nextVarStartAddress = val + bitLength * var.arrayLength;

                    if (nextVarStartAddress > maxAddress)
                    {
                        maxAddress = nextVarStartAddress;
                    }
                }
            }
        }
    }

    return maxAddress;
}

bool VariableManage::modifyModbusaddressAndRW(const long &varID, const int &modbusaddress, const QString &modbusrw)
{
    // qDebug() << "modifyModbusaddressAndRW" << varID << modbusaddress << modbusrw;

    VariableList currVar;
    currVar.id = varID;
    QSqlError daoError = qx::dao::fetch_by_id(currVar, &m_db);

    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoError.text().toStdString());
        return false;
    }

    currVar.modbusAddress = modbusaddress;
    currVar.modbusRw = modbusrw;
    currVar.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");

    // 校验modbus地址是否发生踩踏
    if (!checkAddressTrample(currVar))
    {
        return false;
    }

    daoError = qx::dao::update(currVar, &m_db);

    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update VariableList:" + daoError.text().toStdString());
        return false;
    }

    emit variableChanged(currVar.settingName, currVar.owned, "modify", currVar.name);
    return true;
}

// 根据deviceName和Owned、变量名修改 segement 和 offset的值 // 段号0 - 32 、偏移0 - 65535
QString VariableManage::modifySegementAndOffset(const QString &deviceName, const QString &owned, const QString &name,
                                                const int &segement, const int &offset)
{
    // 获取所有相关记录，然后在应用层进行不区分大小写的名称过滤
    qx_query query;
    query.where("deviceName").isEqualTo(deviceName).and_("owned").isEqualTo(owned);
    QList<VariableList> allVars;
    QSqlError daoError = qx::dao::fetch_by_query(query, allVars, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoError.text().toStdString());
        return QString("数据错误!!!");
    }

    // 在应用层进行不区分大小写的名称过滤
    VariableList var;
    bool found = false;
    for (const auto &v : allVars)
    {
        if (QString::compare(v.name, name, Qt::CaseInsensitive) == 0)
        {
            var = v;
            found = true;
            break;
        }
    }

    if (!found)
    {
        return QString("未找到指定的变量!!!");
    }
    if (segement < 0 || segement > 32)
    {
        return QString("段号超出有效范围, 应为0 - 32");
    }
    if (offset < 0 || offset > 65535)
    {
        return QString("偏移超出有效范围, 应为0 - 65535");
    }

    var.segement = segement;
    var.offset = offset;
    daoError = qx::dao::update(var, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update VariableList:" + daoError.text().toStdString());
        return QString("数据错误!!!");
    }
    emit variableChanged(var.settingName, var.owned, "modify", var.name);
    return QString();
}

bool VariableManage::deleteVariable(const long &varID)
{
    VariableList currVar;
    currVar.id = varID;
    // 根据id查询变量
    QSqlError daoError = qx::dao::fetch_by_id(currVar, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoError.text().toStdString());
        return false;
    }
    // 查询所有settingName为currVar.settingName和name为currVar.name的变量
    QList<VariableList> varList;
    qx_query query;
    query.where("settingName").isEqualTo(currVar.settingName).and_("name").isEqualTo(currVar.name);
    daoError = qx::dao::fetch_by_query(query, varList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoError.text().toStdString());
        return false;
    }
    for (auto var : varList)
    {
        daoError = qx::dao::delete_by_id(currVar, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT("delete VariableList:" + daoError.text().toStdString());
            return false;
        }
        emit variableChanged(var.settingName, var.owned, "delete", var.name);
    }
    // 删除MonitorList中varID = varID的变量
    query.clear();
    query.where("varID").isEqualTo(QVariant::fromValue(varID));
    qx::dao::delete_by_query<MonitorList>(query, &m_db);

    // 删除ForcedValueList中varID = varID的变量
    query.where("varID").isEqualTo(QVariant::fromValue(varID));
    qx::dao::delete_by_query<ForcedValueList>(query, &m_db);

    return true;
}

bool VariableManage::deleteVariableByOwned(const QString &settingName, const QString &owned)
{
    qx_query query;
    query.where("settingName").isEqualTo(settingName).and_("owned").isEqualTo(owned);
    QList<VariableList> variablelist;
    QSqlError daoError = qx::dao::fetch_by_query(query, variablelist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }
    daoError = qx::dao::delete_by_query<VariableList>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("delete VariableList:" + daoError.text().toStdString());
        return false;
    }
    // 遍历variablelist中的每个变量，在MonitorList和ForcedValueList中也删除
    for (auto var : variablelist)
    {
        // 删除MonitorList中varID = var.id的变量
        query.clear();
        query.where("varID").isEqualTo(QVariant::fromValue(var.id));
        qx::dao::delete_by_query<MonitorList>(query, &m_db);

        // 删除ForcedValueList中varID = var.id的变量
        qx::dao::delete_by_query<ForcedValueList>(query, &m_db);

        emit variableChanged(var.settingName, var.owned, "delete", var.name);
    }

    return true;
}

bool VariableManage::deleteVariableByName(const QString &settingName, const QString &name)
{
    // 获取所有相关记录，然后在应用层进行不区分大小写的名称过滤
    qx_query query;
    query.where("settingName").isEqualTo(settingName);
    QList<VariableList> allVariables;
    QSqlError daoError = qx::dao::fetch_by_query(query, allVariables, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }

    // 在应用层进行不区分大小写的名称过滤
    QList<VariableList> variablelist;
    for (const auto &var : allVariables)
    {
        if (QString::compare(var.name, name, Qt::CaseInsensitive) == 0)
        {
            variablelist.append(var);
        }
    }

    // 删除匹配的变量
    for (auto &var : variablelist)
    {
        daoError = qx::dao::delete_by_id(var, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT("delete VariableList:" + daoError.text().toStdString());
            return false;
        }
    }

    // 遍历variablelist中的每个变量，在MonitorList和ForcedValueList中也删除
    for (auto var : variablelist)
    {
        // 删除MonitorList中varID = var.id的变量
        query.clear();
        query.where("varID").isEqualTo(QVariant::fromValue(var.id));
        qx::dao::delete_by_query<MonitorList>(query, &m_db);

        // 删除ForcedValueList中varID = var.id的变量
        qx::dao::delete_by_query<ForcedValueList>(query, &m_db);

        emit variableChanged(var.settingName, var.owned, "delete", var.name);
    }
    return true;
}

bool VariableManage::deleteDeviceVariable(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::delete_by_query<VariableList>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete VariableList:" + daoError.text().toStdString());
        return false;
    }
    emit variableChanged(deviceName, "", "delete", "");
    return true;
}

bool VariableManage::quoteVariable(const long &sourceVarID, const QString &target_owned, const QString &target_type)
{
    VariableList sourceVar;
    sourceVar.id = sourceVarID;
    QSqlError daoError = qx::dao::fetch_by_id(sourceVar, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search VariableList:" + daoError.text().toStdString());
        return false;
    }

    // 查询重名变量
    qx_query query;
    query.where("settingName")
        .isEqualTo(sourceVar.settingName)
        .and_("owned")
        .isEqualTo(target_owned)
        .and_("name")
        .isEqualTo(sourceVar.name);
    long count = qx::dao::count<VariableList>(query, &m_db);
    if (count > 0)
    {
        LOG_ERROR_DEFAULT("变量名重复");
        return false;
    }

    VariableList targetVar;
    targetVar.settingName = sourceVar.settingName;
    targetVar.scope = sourceVar.scope;
    targetVar.owned = target_owned;
    targetVar.type = target_type;
    targetVar.name = sourceVar.name;
    targetVar.dataType = sourceVar.dataType;
    targetVar.dataTypeID = sourceVar.dataTypeID;
    targetVar.arrayLength = sourceVar.arrayLength;
    targetVar.address = sourceVar.address;
    targetVar.isConstant = sourceVar.isConstant;
    targetVar.isOpc = sourceVar.isOpc;
    targetVar.isRetained = sourceVar.isRetained;
    targetVar.description = sourceVar.description;
    targetVar.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    targetVar.lastModifyTime = sourceVar.createTime;
    targetVar.isShow = sourceVar.isShow;
    targetVar.initialValue = sourceVar.initialValue;
    targetVar.isSelected = sourceVar.isSelected;
    targetVar.state = sourceVar.state;
    targetVar.modbusAddress = sourceVar.modbusAddress;
    targetVar.modbusRw = sourceVar.modbusRw;
    targetVar.segement = sourceVar.segement;
    targetVar.offset = sourceVar.offset;

    targetVar.masterNo = sourceVar.masterNo;
    targetVar.slaveNo = sourceVar.slaveNo;
    targetVar.slotNo = sourceVar.slotNo;
    targetVar.modelNumber = sourceVar.modelNumber;
    targetVar.priority = sourceVar.priority;
    targetVar.isRedundancy = sourceVar.isRedundancy;

    daoError = qx::dao::insert(targetVar, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("insert VariableList:" + daoError.text().toStdString());
        return false;
    }
    emit variableChanged(targetVar.settingName, targetVar.owned, "add", targetVar.name);
    return true;
}

bool VariableManage::autoAssignModbusAddresses(const QList<long> &varIDs, int startingAddress)
{
    int currentAddress = startingAddress;

    for (const long &varID : varIDs)
    {
        // 根据 ID 获取变量
        VariableList variable;
        variable.id = varID;
        QSqlError daoError = qx::dao::fetch_by_id(variable, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("Failed to fetch VariableList:" + daoError.text().toStdString());
            return false;
        }

        // 变量的数据类型
        QString dataType = variable.dataType;
        // 变量的长度
        int arrayLength = variable.arrayLength;
        // 获取数据类型对应的位长度
        int bitLength = getBitLengthFromName(variable.settingName, dataType);

        if (bitLength == 0)
        {
            LOG_ERROR_DEFAULT("Unknown data type: " + dataType.toStdString());
            return false;
        }

        if (bitLength < 8)
        {
            bitLength = 1;
        }
        else
        {
            bitLength = bitLength / 8;
        }

        // 计算下一个变量的起始地址
        int nextStartAddress = bitLength * arrayLength;

        // 分配 ModbusAddress
        variable.modbusAddress = currentAddress;

        // 更新变量
        daoError = qx::dao::update(variable, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("Failed to update VariableList:" + daoError.text().toStdString());
            return false;
        }

        // 更新当前地址
        currentAddress += nextStartAddress;

        emit variableChanged(variable.settingName, variable.owned, "add", variable.name);
    }

    return true;
}

// 按照TableName字段升序获取所有数据
QJsonArray VariableManage::getMonitorTableList(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).orderAsc("tableName");
    QList<MonitorList> monitorList;
    QSqlError daoError = qx::dao::fetch_by_query(query, monitorList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QJsonArray();
    }
    QJsonArray array;
    for (auto dy : monitorList)
    {
        QJsonObject obj;
        obj["id"] = static_cast<int>(dy.id);
        obj["settingName"] = dy.settingName;
        obj["tableName"] = dy.tableName;
        obj["varID"] = static_cast<int>(dy.varID);
        obj["monitorName"] = dy.monitorName;
        obj["monitoredValue"] = dy.monitoredValue;
        obj["displayFormat"] = dy.displayFormat;
        obj["monitoringWithTrigger"] = dy.monitoringWithTrigger;
        obj["modifyWithTrigger"] = dy.modifyWithTrigger;
        obj["modifyValue"] = dy.modifyValue;
        obj["isModify"] = dy.isModify;
        obj["description"] = dy.description;
        obj["state"] = dy.state;
        obj["scope"] = dy.scope;
        obj["owned"] = dy.owned;
        obj["type"] = dy.type;
        obj["dataType"] = dy.dataType;
        obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);

        array.append(obj);
    }
    return array;
}
//
QJsonArray VariableManage::getMonitorTableName(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QList<MonitorList> monitorList;
    QSqlError daoError = qx::dao::fetch_by_query(query, monitorList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QJsonArray();
    }
    QSet<QString> uniqueFields;
    for (auto dy : monitorList)
    {
        uniqueFields.insert(dy.tableName);
    }
    QJsonArray array = QJsonArray::fromStringList(QStringList::fromSet(uniqueFields));
    qInfo() << "[array]:" << array;
    return array;
}

QVector<QStringList> VariableManage::getMonitorVec(const QString &deviceName)
{
    QVector<QStringList> vec;
    QList<MonitorList> monitorList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::fetch_by_query(query, monitorList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return vec;
    }
    if (!monitorList.isEmpty())
    {
        for (auto dy : monitorList)
        {
            QStringList list;
            list << QString::number(dy.id) << dy.settingName << dy.tableName << QString::number(dy.varID)
                 << dy.monitorName << dy.monitoredValue << dy.displayFormat << QString::number(dy.monitoringWithTrigger)
                 << QString::number(dy.modifyWithTrigger) << dy.modifyValue << QString::number(dy.isModify)
                 << dy.description << QString::number(dy.state);
            vec.append(list);
        }
    }
    return vec;
}

// 获取监视表的所有数据
QJsonArray VariableManage::getAllMonitorList()
{
    QJsonArray array;

    QList<MonitorList> monitorList;
    QSqlError daoError = qx::dao::fetch_all(monitorList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!monitorList.isEmpty())
    {
        for (auto dy : monitorList)
        {
            QJsonObject obj;
            obj["id"] = static_cast<int>(dy.id);
            obj["settingName"] = dy.settingName;
            obj["tableName"] = dy.tableName;
            obj["varID"] = static_cast<int>(dy.varID);
            obj["monitorName"] = dy.monitorName;
            obj["monitoredValue"] = dy.monitoredValue;
            obj["displayFormat"] = dy.displayFormat;
            obj["monitoringWithTrigger"] = dy.monitoringWithTrigger;
            obj["modifyWithTrigger"] = dy.modifyWithTrigger;
            obj["modifyValue"] = dy.modifyValue;
            obj["isModify"] = dy.isModify;
            obj["description"] = dy.description;
            obj["state"] = dy.state;

            array.append(obj);
        }
    }
    return array;
}

// 根据settingName(deviceName)和tabaleName获取对应的监视表数据
QJsonArray VariableManage::getMonitorList(const QString &deviceName, const QString &tableName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("tableName").isEqualTo(tableName);
    QList<MonitorList> monitorList;
    QSqlError daoError = qx::dao::fetch_by_query(query, monitorList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QJsonArray();
    }
    QJsonArray array;
    for (auto dy : monitorList)
    {
        // QJsonObject varObj = getVariableFromID(static_cast<int>(dy.varID));
        QJsonObject obj;
        obj["id"] = static_cast<int>(dy.id);
        obj["settingName"] = dy.settingName;
        obj["tableName"] = dy.tableName;
        obj["varID"] = static_cast<int>(dy.varID);
        obj["monitorName"] = dy.monitorName;
        obj["monitoredValue"] = dy.monitoredValue;
        obj["displayFormat"] = dy.displayFormat;
        obj["monitoringWithTrigger"] = dy.monitoringWithTrigger;
        obj["modifyWithTrigger"] = dy.modifyWithTrigger;
        obj["modifyValue"] = dy.modifyValue;
        obj["isModify"] = dy.isModify;
        obj["description"] = dy.description;
        obj["state"] = dy.state;
        obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);
        obj["dataType"] = dy.dataType;
        obj["arrayLength"] = 1;
        obj["owned"] = dy.owned;
        obj["scope"] = dy.scope;
        obj["type"] = dy.type;

        array.append(obj);
    }

    return array;
}

QList<MonitorList> VariableManage::getMonitorList(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).orderAsc("tableName");
    QList<MonitorList> monitorList;
    QSqlError daoError = qx::dao::fetch_by_query(query, monitorList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return monitorList;
}

// 添加监视表变量
QString VariableManage::addMonitorVariable(const QString &deviceName, const QString &tableName, const long &varID,
                                        QString monitorName, QString monitoredValue, QString displayFormat,
                                        int monitoringWithTrigger, int modifyWithTrigger, QString modifyValue,
                                        bool isModify, QString description, int state, QString scope, QString owned,
                                        QString type, QString dataType, const long &dataTypeID)
{
    // 获取where("settingName").isEqualTo(deviceName).and_("tableName").isEqualTo(tableName)的所有数据
    QList<MonitorList> monitorList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("tableName").isEqualTo(tableName);
    QSqlError daoError = qx::dao::fetch_by_query(query, monitorList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QString("数据库查询失败");
    }
    if (monitorList.size() > 1000)
    {
        LOG_ERROR_DEFAULT("监视表变量数量超过1000");
        return QString("监视表变量数量超过1000");
    }
    if (!monitorList.isEmpty())
    {
        for (auto dy : monitorList)
        {
            if (dy.monitorName == monitorName)
            {
                LOG_ERROR_DEFAULT("Variable name repetition");
                // 重复就更新数据
                modifyMonitorVariable(dy.id, deviceName, tableName, dy.varID, monitorName, monitoredValue,
                                      displayFormat, monitoringWithTrigger, modifyWithTrigger, modifyValue, isModify,
                                      description, state);

                return QString();
            }
        }
    }
    MonitorList newMonitor;
    newMonitor.settingName = deviceName;
    newMonitor.tableName = tableName;
    newMonitor.varID = varID;
    newMonitor.monitorName = monitorName;
    newMonitor.monitoredValue = monitoredValue;
    newMonitor.displayFormat = displayFormat;
    newMonitor.monitoringWithTrigger = monitoringWithTrigger;
    newMonitor.modifyWithTrigger = modifyWithTrigger;
    newMonitor.modifyValue = modifyValue;
    newMonitor.isModify = isModify;
    newMonitor.description = description.replace(",", "，");
    newMonitor.state = state;
    newMonitor.scope = scope;
    newMonitor.owned = owned;
    newMonitor.type = type;
    newMonitor.dataType = dataType;
    newMonitor.dataTypeID = dataTypeID;
    // 插入
    daoError = qx::dao::insert(newMonitor, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("insert MonitorList:" + daoError.text().toStdString());
        return QString("数据库插入失败");
    }
    emit monitorChanged(newMonitor.settingName, newMonitor.tableName, "add", newMonitor.monitorName);
    return QString();
}

// 添加监视表变量 - 批量添加版本
QString VariableManage::addMonitorVariable(const QString &deviceName, const QString &tableName,
                                           QList<QVariantMap> monitorList)
{
    // 参数验证
    if (deviceName.isEmpty() || tableName.isEmpty())
    {
        LOG_ERROR_DEFAULT("设备名称或表名称不能为空");
        return QString("设备名称或表名称不能为空");
    }

    if (monitorList.isEmpty())
    {
        LOG_ERROR_DEFAULT("监视变量列表不能为空");
        return QString("监视变量列表不能为空");
    }

    // 检查当前表中的变量数量，避免超过限制
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("tableName").isEqualTo(tableName);
    long currentCount = qx::dao::count<MonitorList>(query, &m_db);
    if (currentCount + monitorList.size() > 1000)
    {
        LOG_ERROR_DEFAULT("添加后监视表变量数量将超过1000");
        return QString("添加后监视表变量数量将超过1000");
    }

    // 批量处理每个监视变量
    bool allSuccess = true;
    for (const QVariantMap &varMap : monitorList)
    {
        // 从 QVariantMap 中提取字段
        long varID = varMap.value("ID", 0).toLongLong();
        QString monitorName = varMap.value("dataName", "").toString();
        QString scope = varMap.value("scope", "").toString();
        QString owned = varMap.value("owned", "").toString();
        QString type = varMap.value("type", "").toString();
        QString dataType = varMap.value("dataType", "").toString();
        long dataTypeID = varMap.value("dataTypeID", 0).toLongLong();
        QString description = varMap.value("description", "").toString();

        // 设置默认参数
        QString monitoredValue = "";
        QString displayFormat = "十进制";
        int monitoringWithTrigger = 0;
        int modifyWithTrigger = 0;
        QString modifyValue = "";
        bool isModify = true;
        int state = 0;

        // 验证必要字段
        if (varID <= 0 || monitorName.isEmpty())
        {
            LOG_ERROR_DEFAULT(QString("无效的变量数据: ID=%1, dataName=%2").arg(varID).arg(monitorName));
            continue;
        }

        // 调用现有的详细 addMonitorVariable 函数
        QString result = addMonitorVariable(deviceName, tableName, varID, monitorName,
                                          monitoredValue, displayFormat, monitoringWithTrigger,
                                          modifyWithTrigger, modifyValue, isModify, description,
                                          state, scope, owned, type, dataType, dataTypeID);

        // 检查添加结果（空字符串表示成功，非空字符串表示错误信息）
        if (!result.isEmpty())
        {
            LOG_ERROR_DEFAULT(QString("添加监视变量失败: %1, 错误: %2").arg(monitorName).arg(result));
            return QString("添加监视变量失败: %1, 错误: %2").arg(monitorName).arg(result);
        }
    }

    return QString();
}

// 删除监视表
bool VariableManage::deleteMonitorVariable(const long &id)
{
    MonitorList currMonitor;
    currMonitor.id = id;
    QSqlError daoError = qx::dao::delete_by_id(currMonitor, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("delete MonitorList:" + daoError.text().toStdString());
        return false;
    }
    emit monitorChanged(currMonitor.settingName, currMonitor.tableName, "delete", currMonitor.monitorName);
    return true;
}

bool VariableManage::modifyMonitorVariable(MonitorList monitor)
{
    QSqlError daoError = qx::dao::update(monitor, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update MonitorList:" + daoError.text().toStdString());
        return false;
    }
    emit monitorChanged(monitor.settingName, monitor.tableName, "modify", monitor.monitorName);
    return true;
}

template <typename T> bool VariableManage::insert_tableData(T &a_tableData)
{
    QSqlError daoError = qx::dao::insert(a_tableData, const_cast<QSqlDatabase *>(&m_db));
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }
    return true;
}

template <typename T> bool VariableManage::update_tableData(T &a_tableData)
{
    QSqlError daoError = qx::dao::save(a_tableData, const_cast<QSqlDatabase *>(&m_db));
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }
    return true;
}

///
/// \brief MasterSlaveOperate::get_Table_ByID
/// \param content_List
/// \param ID
/// \return  成功：true， 失败:false
///
template <typename T> bool VariableManage::get_Table_ByID(T &content_List, const long &ID)
{
    content_List.clear();
    qx_query query;
    query.where("ID").isEqualTo(static_cast<int>(ID));
    QSqlError daoError = qx::dao::fetch_by_query(query, content_List, const_cast<QSqlDatabase *>(&m_db));
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }
    return true;
}

///
/// \brief MasterSlaveOperate::get_Table_ByAQMap
/// \param content_List
/// \param key_Map
/// \return
///
template <typename T> bool VariableManage::get_Table_ByAQMap(T &content_List, QMap<QString, QVariant> &key_Map)
{
    content_List.clear();
    qx_query query;
    auto it = key_Map.begin();
    bool isFirst = true;
    while (it != key_Map.end())
    {
        if (isFirst)
        {
            query.where(it.key()).isEqualTo(it.value());
            isFirst = false;
        }
        else
        {
            query.and_(it.key()).isEqualTo(it.value());
        }
        ++it;
    }
    QSqlError daoError = qx::dao::fetch_by_query(query, content_List, const_cast<QSqlDatabase *>(&m_db));
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return false;
    }
    return true;
}

// 修改监视表
bool VariableManage::modifyMonitorVariable(const long &id, const QString &deviceName, const QString &tableName,
                                           const long &varID, QString monitorName, QString monitoredValue,
                                           QString displayFormat, int monitoringWithTrigger, int modifyWithTrigger,
                                           QString modifyValue, bool isModify, QString description, int state)
{
    MonitorList currMonitor;
    currMonitor.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(currMonitor, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search MonitorList:" + daoError.text().toStdString());
        return false;
    }
    // 修改
    currMonitor.settingName = deviceName;
    currMonitor.tableName = tableName;
    currMonitor.varID = varID;
    currMonitor.monitorName = monitorName;
    currMonitor.monitoredValue = monitoredValue;
    currMonitor.displayFormat = displayFormat;
    currMonitor.monitoringWithTrigger = monitoringWithTrigger;
    currMonitor.modifyWithTrigger = modifyWithTrigger;
    currMonitor.modifyValue = modifyValue;
    currMonitor.isModify = isModify;
    currMonitor.description = description.replace(",", "，");
    currMonitor.state = state;
    daoError = qx::dao::update(currMonitor, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update MonitorList:" + daoError.text().toStdString());
        return false;
    }
    emit monitorChanged(currMonitor.settingName, currMonitor.tableName, "modify", currMonitor.monitorName);
    return true;
}

bool VariableManage::deleteDeviceMonitor(const QString &deviceName, const QString &tableName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("tableName").isEqualTo(tableName);
    QSqlError daoError = qx::dao::delete_by_query<MonitorList>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete MonitorList:" + daoError.text().toStdString());
        return false;
    }
    emit monitorChanged(deviceName, tableName, "delete", "");
    return true;
}

bool VariableManage::deleteDeviceMonitor(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::delete_by_query<MonitorList>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete MonitorList:" + daoError.text().toStdString());
        return false;
    }
    emit monitorChanged(deviceName, "", "delete", "");
    return true;
}

QJsonArray VariableManage::getForcedTableList(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).orderAsc("tableName");
    QList<ForcedValueList> forcedList;
    QSqlError daoError = qx::dao::fetch_by_query(query, forcedList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QJsonArray();
    }
    QJsonArray array;
    for (auto dy : forcedList)
    {
        QJsonObject obj;
        obj["id"] = static_cast<int>(dy.id);
        obj["settingName"] = dy.settingName;
        obj["tableName"] = dy.tableName;
        obj["varID"] = static_cast<int>(dy.varID);
        obj["forceName"] = dy.forceName;
        obj["monitoredValue"] = dy.monitoredValue;
        obj["displayFormat"] = dy.displayFormat;
        obj["monitoringWithTrigger"] = dy.monitoringWithTrigger;
        obj["forcedValue"] = dy.forcedValue;
        obj["isForced"] = dy.isForced;
        obj["description"] = dy.description;
        obj["state"] = dy.state;
        obj["scope"] = dy.scope;
        obj["owned"] = dy.owned;
        obj["type"] = dy.type;
        obj["dataType"] = dy.dataType;
        obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);

        array.append(obj);
    }
    return array;
}

QJsonArray VariableManage::getForcedTableName(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QList<ForcedValueList> forcedList;
    QSqlError daoError = qx::dao::fetch_by_query(query, forcedList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QJsonArray();
    }
    QSet<QString> uniqueFields;
    for (auto dy : forcedList)
    {
        uniqueFields.insert(dy.tableName);
    }
    QJsonArray array = QJsonArray::fromStringList(QStringList::fromSet(uniqueFields));
    qInfo() << "[array]:" << array;
    return array;
}

// 获取根据deviceName变量表的对应的所有数据
QVector<QStringList> VariableManage::getForcedVec(const QString &deviceName)
{
    QVector<QStringList> vec;
    QList<ForcedValueList> forcedList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::fetch_by_query(query, forcedList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return vec;
    }
    if (!forcedList.isEmpty())
    {
        for (auto dy : forcedList)
        {
            QStringList list;
            list << QString::number(dy.id) << dy.settingName << dy.tableName << QString::number(dy.varID)
                 << dy.forceName << dy.monitoredValue << dy.displayFormat << QString::number(dy.monitoringWithTrigger)
                 << dy.forcedValue << QString::number(dy.isForced) << dy.description << QString::number(dy.state);
            vec.append(list);
        }
    }
    return vec;
}

// 获取强指表的所有数据
QJsonArray VariableManage::getAllForcedList()
{
    qx_query query;
    QList<ForcedValueList> forcedList;
    QSqlError daoError = qx::dao::fetch_all(forcedList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QJsonArray();
    }

    QJsonArray array;
    for (auto dy : forcedList)
    {
        QJsonObject obj;
        obj["id"] = static_cast<int>(dy.id);
        obj["settingName"] = dy.settingName;
        obj["tableName"] = dy.tableName;
        obj["varID"] = static_cast<int>(dy.varID);
        obj["forceName"] = dy.forceName;
        obj["monitoredValue"] = dy.monitoredValue;
        obj["displayFormat"] = dy.displayFormat;
        obj["monitoringWithTrigger"] = dy.monitoringWithTrigger;
        obj["forcedValue"] = dy.forcedValue;
        obj["isForced"] = dy.isForced;
        obj["description"] = dy.description;
        obj["state"] = dy.state;
        array.append(obj);
    }
    return array;
}

QJsonArray VariableManage::getForcedList(const QString &deviceName, const QString &tableName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("tableName").isEqualTo(tableName);
    QList<ForcedValueList> forcedList;
    QSqlError daoError = qx::dao::fetch_by_query(query, forcedList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return QJsonArray();
    }
    QJsonArray array;
    for (auto dy : forcedList)
    {
        QJsonObject obj;
        obj["id"] = static_cast<int>(dy.id);
        obj["settingName"] = dy.settingName;
        obj["tableName"] = dy.tableName;
        obj["varID"] = static_cast<int>(dy.varID);
        obj["forceName"] = dy.forceName;
        obj["monitoredValue"] = dy.monitoredValue;
        obj["displayFormat"] = dy.displayFormat;
        obj["monitoringWithTrigger"] = dy.monitoringWithTrigger;
        obj["forcedValue"] = dy.forcedValue;
        obj["isForced"] = dy.isForced;
        obj["description"] = dy.description;
        obj["state"] = dy.state;
        obj["scope"] = dy.scope;
        obj["owned"] = dy.owned;
        obj["type"] = dy.type;
        obj["dataType"] = dy.dataType;
        obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);
        array.append(obj);
    }
    return array;
}

bool VariableManage::addForcedVariable(const QString &deviceName, const QString &tableName, const long &varID,
                                       const QString &forceName, const QString &monitoredValue,
                                       const QString &displayFormat, const int &monitoringWithTrigger,
                                       const QString &forcedValue, const int &isForced, QString description,
                                       const int &state, QString scope, QString owned, QString type, QString dataType,
                                       const long &dataTypeID)
{
    qx_query query;
    query.where("settingName")
        .isEqualTo(deviceName)
        .and_("tableName")
        .isEqualTo(tableName)
        .and_("varID")
        .isEqualTo(QVariant::fromValue(varID));
    long count = qx::dao::count<ForcedValueList>(query, &m_db);
    if (count > 0)
    {
        return false;
    }
    ForcedValueList newForced;
    newForced.settingName = deviceName;
    newForced.tableName = tableName;
    newForced.varID = varID;
    newForced.forceName = forceName;
    newForced.monitoredValue = monitoredValue;
    newForced.displayFormat = displayFormat;
    newForced.monitoringWithTrigger = monitoringWithTrigger;
    newForced.forcedValue = forcedValue;
    newForced.isForced = isForced;
    newForced.description = description.replace(",", "，");
    newForced.state = state;
    newForced.scope = scope;
    newForced.owned = owned;
    newForced.type = type;
    newForced.dataType = dataType;
    newForced.dataTypeID = dataTypeID;
    // 插入
    QSqlError daoError = qx::dao::insert(newForced, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("insert ForcedValueList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool VariableManage::deleteForcedVariable(const long &id)
{
    ForcedValueList currForced;
    currForced.id = id;
    QSqlError daoError = qx::dao::delete_by_id(currForced, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("delete ForcedValueList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool VariableManage::deleteDeviceForced(const QString &deviceName, const QString &tableName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("tableName").isEqualTo(tableName);
    QSqlError daoError = qx::dao::delete_by_query<ForcedValueList>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete ForcedValueList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool VariableManage::deleteDeviceForced(const QString &deviceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::delete_by_query<ForcedValueList>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete ForcedValueList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool VariableManage::modifyForcedVariable(const long &id, const QString &deviceName, const QString &tableName,
                                          const long &varID, const QString &forceName, const QString &monitoredValue,
                                          const QString &displayFormat, const int &monitoringWithTrigger,
                                          const QString &forcedValue, const int &isForced, QString description,
                                          const int &state)
{
    ForcedValueList currForced;
    currForced.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(currForced, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search ForcedValueList:" + daoError.text().toStdString());
        return false;
    }
    // 修改
    currForced.settingName = deviceName;
    currForced.tableName = tableName;
    currForced.varID = varID;
    currForced.forceName = forceName;
    currForced.monitoredValue = monitoredValue;
    currForced.displayFormat = displayFormat;
    currForced.monitoringWithTrigger = monitoringWithTrigger;
    currForced.forcedValue = forcedValue;
    currForced.isForced = isForced;
    currForced.description = description.replace(",", "，");
    currForced.state = state;
    daoError = qx::dao::update(currForced, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update ForcedValueList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

QJsonArray VariableManage::getFunctionBlockList(const QString &deviceName, const QStringList &owned,
                                                const QStringList &type)
{
    QVariantList ownedList;
    for (auto dy : owned)
    {
        ownedList.append(dy);
    }
    QVariantList typeList;
    for (auto dy : type)
    {
        typeList.append(dy);
    }
    QJsonArray array;
    QList<FBReferenceList> fbList;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("owned").in(ownedList).and_("type").in(typeList);
    QSqlError daoError = qx::dao::fetch_by_query(query, fbList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
        return array;
    }
    if (!fbList.isEmpty())
    {
        for (auto dy : fbList)
        {
            QJsonObject obj;
            obj["fid"] = static_cast<int>(dy.id);
            obj["scope"] = dy.scope;
            obj["owned"] = dy.owned;
            obj["type"] = dy.type;
            obj["referenceName"] = dy.referenceName;
            obj["instanceName"] = dy.instanceName;
            obj["fbType"] = dy.fbType;
            array.append(obj);
        }
    }
    return array;
}

bool VariableManage::addFunctionBlock(const QString &deviceName, const QString &scope, const QString &owned,
                                      const QString &type, const QString &referenceName, const QString &instanceName,
                                      const QString &fbType)
{
    // 创建一个本地变量作为referenceName的副本
    QString actualReferenceName = referenceName;
    // 如果fbType = FUNCTION，referenceName = instanceName
    if (fbType == "FUNCTION")
    {
        if (referenceName != instanceName)
        {
            LOG_ERROR_DEFAULT("referenceName != instanceName");
            return false;
        }
        // 查询同owned下instanceName是否存在
        qx_query query;
        query.where("settingName")
            .isEqualTo(deviceName)
            .and_("owned")
            .isEqualTo(owned)
            .and_("instanceName")
            .isEqualTo(referenceName);
        long count = qx::dao::count<FBReferenceList>(query, &m_db);
        if (count > 0)
        {
            LOG_ERROR_DEFAULT("referenceName already exists");
            return false;
        }
    }
    else
    {
        // FunctionBlock
        // 查询referenceName是否存在
        // 查询重名变量
        qx_query query;
        query.where("settingName").isEqualTo(deviceName).and_("referenceName").isEqualTo(referenceName);
        long count = qx::dao::count<FBReferenceList>(query, &m_db);
        if (count > 0)
        {
            // 使用当前时间作为随机数种子
            qsrand(QDateTime::currentDateTime().toTime_t());
            // 生成五位随机整数串
            QString randomString = QString::number(qrand() % 100000);
            actualReferenceName = actualReferenceName + randomString;
        }
    }
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(actualReferenceName))
    {
        LOG_ERROR_DEFAULT(actualReferenceName.toStdString() + " is invalid");
        return false;
    }

    qInfo() << fbType;
    FBReferenceList newFB;
    newFB.settingName = deviceName;
    newFB.scope = scope;
    newFB.owned = owned;
    newFB.type = type;
    newFB.referenceName = actualReferenceName;
    newFB.instanceName = instanceName;
    newFB.fbType = fbType;
    newFB.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    newFB.lastModifyTime = newFB.createTime;

    QSqlError daoError = qx::dao::insert(newFB, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("insert FBListList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool VariableManage::modifyFunctionBlock(const long &id, const QString &referenceName)
{
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(referenceName))
    {
        LOG_ERROR_DEFAULT(referenceName.toStdString() + " is invalid");
        return false;
    }
    FBReferenceList currFB;
    currFB.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(currFB, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("search FBReferenceList:" + daoError.text().toStdString());
        return false;
    }
    // 查询重名变量
    qx_query query;
    query.where("referenceName").isEqualTo(referenceName);
    FBReferenceList currFBTemp;
    QSqlError daoErrorTemp = qx::dao::fetch_by_query(query, currFBTemp, &m_db);
    long count = qx::dao::count<FBReferenceList>(query, &m_db);
    if (count > 0 && currFBTemp.id != id)
    {
        LOG_ERROR_DEFAULT("FB count > 0");
        return false;
    }
    currFB.referenceName = referenceName;
    currFB.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    daoError = qx::dao::update(currFB, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update FBReferenceList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool VariableManage::deleteFunctionBlock(const long &id)
{
    FBReferenceList currFB;
    currFB.id = id;
    QSqlError daoError = qx::dao::delete_by_id(currFB, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("delete FBReferenceList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

QString VariableManage::conversion(const QString value, const QString fromValue, const QString toValue)
{
    int fromBase;
    int toBase;

    if (fromValue == "二进制")
    {
        fromBase = 2;
    }
    else if (fromValue == "八进制")
    {
        fromBase = 8;
    }
    else if (fromValue == "十六进制")
    {
        fromBase = 16;
    }
    else
    {
        fromBase = 10;
    }

    if (toValue == "八进制")
    {
        toBase = 8;
    }
    else if (toValue == "十六进制")
    {
        toBase = 16;
    }
    else if (toValue == "二进制")
    {
        toBase = 2;
    }
    else
    {
        toBase = 10;
    }

    bool ok;
    int decimalValue = value.toInt(&ok, fromBase); // 将输入的字符串转换为十进制整数
    if (!ok)
    {
        return "Invalid number for the given base";
    }

    QString result = QString::number(decimalValue, toBase).toUpper(); // 将十进制整数转换为指定进制的字符串

    return result;
}

// 根据SettingName和Owned删除引用
bool VariableManage::deleteFbreferenceSettingName(const QString &settingName, const QString &owned)
{
    qx_query query;
    query.where("settingName").isEqualTo(settingName).and_("owned").isEqualTo(owned);
    QSqlError daoError = qx::dao::delete_by_query<FBReferenceList>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete FBReferenceList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool VariableManage::deleteFbreferenceFromID(const long &id)
{
    FBReferenceList fbr;
    fbr.id = id;
    QSqlError daoError = qx::dao::delete_by_id(fbr, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete FBReferenceList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

QList<FBReferenceList> VariableManage::getFBReferenceListFromSetteingName(const QString &settingName)
{
    qx_query query;
    query.where("settingName").isEqualTo(settingName);
    QList<FBReferenceList> list;
    QSqlError daoError = qx::dao::fetch_by_query(query, list, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("get FBReferenceList:" + daoError.text().toStdString());
    }

    return list;
}

// 根据设备名重置所有设备名为 settingName 的行，将 monitoringWithTrigger 设置为 0，forcedValue 设置为空
// 根据设定名称重置相关设备的配置

bool VariableManage::resetDeviceForced(const QString &settingName)
{
    // 构造一个 SQL 查询来指定更新操作
    QString queryStr =
        "UPDATE ForcedValueList SET MonitoringWithTrigger = 0, ForcedValue = NULL WHERE SettingName = :SettingName";
    qx::QxSqlQuery query(queryStr);
    query.bind(":SettingName", settingName);

    // 执行更新操作
    QSqlError daoError = qx::dao::call_query(query, &m_db);

    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete FBReferenceList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

// 根据SettingName和instanceName删除引用表中的数据
bool VariableManage::deleteFbreferenceListData(const QString &settingName, const QString &instanceName)
{
    qx_query query;
    query.where("settingName").isEqualTo(settingName).and_("instanceName").isEqualTo(instanceName);
    QSqlError daoError = qx::dao::delete_by_query<FBReferenceList>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete FBReferenceList:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool VariableManage::readFile2VariableType(const QString &fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qWarning() << "Cannot open file:" << fileName;
        return false;
    }
    QTextStream in(&file);
    bool global = false;
    bool type = false;
    // QVector<QSharedPointer<VariableType>> types;
    QSharedPointer<VariableType> parent;
    parent.reset();
    int number = 1;
    int sort = 0;
    while (!in.atEnd())
    {
        number = 1;
        QString line = in.readLine().trimmed();
        qDebug() << line;
        // 跳过空行或注释行
        if (line.isEmpty())
        {
            continue;
        }

        // 检查是否遇到类型定义开始
        if (line == "GLOBAL_TYPE_BEGIN")
        {
            global = true;
            continue;
        }

        // 检查是否遇到类型定义结束
        if (line == "GLOBAL_TYPE_END")
        {
            global = false;
            continue;
        }

        // 解析类型定义中的字段
        if (global)
        {
            if (type)
            {
                if (line == "END_TYPE")
                {
                    type = false;
                    sort = 0;
                    continue;
                }
                // 结构体结束
                if (line == "end_struct;")
                {
                    QVector<QSharedPointer<VariableType>> all_variable_type;
                    get_Table_ByID(all_variable_type, parent->parentID);
                    if (all_variable_type.size() > 0)
                    {
                        parent = all_variable_type[0];
                    }
                    else
                    {
                        parent.reset();
                    }
                    continue;
                }

                // 先生成临时数据
                QSharedPointer<VariableType> variable = QSharedPointer<VariableType>(new VariableType);
                variable->type = "EXTEND_TEMP";
                variable->deep = 0;
                variable->mainOffset = 0;
                variable->parentID = 0;
                variable->sortNumber = sort;
                sort++;
                QStringList typeName = line.split(":");
                if (typeName.size() != 2)
                {
                    LOG_ERROR_DEFAULT(QString("Analysis failed").toStdString());
                    return false;
                }
                // 判断是否为数组型
                if (line.contains("array"))
                {
                    QRegularExpression regex(
                        R"((\w+)\s*:\s*array\s*\[\s*((?:\d+\s*\.\.\s*\d+\s*(?:,\s*\d+\s*\.\.\s*\d+\s*)*)?)\s*\]\s*of\s*(\w+)\s*;)");
                    QRegularExpressionMatch match = regex.match(line);
                    if (match.hasMatch())
                    {
                        variable->name = match.captured(1);
                        QStringList strs1 = match.captured(2).trimmed().split(",");
                        if (strs1.size() == 1)
                        {
                            QStringList strs2 = match.captured(2).trimmed().split("..");
                            if (strs2.size() == 2)
                            {
                                variable->arrayCount = strs2[1].toInt() - strs2[0].toInt() + 1;
                            }
                            else
                            {
                                LOG_ERROR_DEFAULT(QString("Analysis failed").toStdString());
                                return false;
                            }
                        }
                        else if (strs1.size() == 2)
                        {
                            QStringList strs2 = strs1[0].trimmed().split("..");
                            if (strs2.size() == 2)
                            {
                                variable->arrayCount = strs2[1].trimmed().toInt() - strs2[0].trimmed().toInt() + 1;
                            }
                            else
                            {
                                LOG_ERROR_DEFAULT(QString("Analysis failed").toStdString());
                                return false;
                            }

                            QStringList strs3 = strs1[1].trimmed().split("..");
                            if (strs3.size() == 2)
                            {
                                number = strs3[1].trimmed().toInt() - strs3[0].trimmed().toInt() + 1;
                            }
                            else
                            {
                                LOG_ERROR_DEFAULT(QString("Analysis failed").toStdString());
                                return false;
                            }
                        }
                        else
                        {
                            LOG_ERROR_DEFAULT(QString("Analysis failed").toStdString());
                            return false;
                        }
                        variable->dataType = match.captured(3);
                    }
                    else
                    {
                        LOG_ERROR_DEFAULT(QString("Analysis failed").toStdString());
                        return false;
                    }
                }
                else
                {
                    variable->name = typeName.first().trimmed();
                    if (typeName.last().trimmed().endsWith(";"))
                    {
                        QString str = typeName.last().trimmed();
                        str.chop(1);
                        variable->dataType = str;
                    }
                    else
                    {
                        variable->dataType = typeName.last().trimmed();
                    }

                    variable->arrayCount = 1;
                }
                // 设置父节点id和深度
                if (!parent.isNull())
                {

                    variable->parentID = parent->id;
                    variable->deep = parent->deep + 1;
                    variable->mainID = parent->mainID;
                    for (int i = 0; i < number; i++)
                    {
                        insert_tableData<QSharedPointer<VariableType>>(variable);
                    }
                }
                else
                {
                    int id = 0;
                    for (int i = 0; i < number; i++)
                    {
                        insert_tableData<QSharedPointer<VariableType>>(variable);
                        if (i == 0)
                        {
                            id = variable->id;
                            variable->mainID = variable->id;
                        }
                        else
                        {
                            variable->mainID = id;
                        }
                        update_tableData<QSharedPointer<VariableType>>(variable);
                    }
                }

                // 通过;判断是否有子节点
                if (!typeName.last().trimmed().endsWith(";")) // 查找父节点
                {
                    parent = variable;
                }
            }

            if (line == "TYPE")
            {
                type = true;
            }
        }
    }

    file.close();

    // 校验重复
    QVector<QSharedPointer<VariableType>> variable_temp;
    get_variable_types("EXTEND_TEMP", variable_temp);
    for (int i = 0; i < variable_temp.size(); i++)
    {
        QVector<QSharedPointer<VariableType>> variables;
        QMap<QString, QVariant> map_device_variable_setting;
        map_device_variable_setting.insert("Name", variable_temp[i]->name);
        map_device_variable_setting.insert("DataType", variable_temp[i]->dataType);
        map_device_variable_setting.insert("Type", "EXTEND");
        if (!get_Table_ByAQMap<QVector<QSharedPointer<VariableType>>>(variables, map_device_variable_setting))
        {
            LOG_ERROR_DEFAULT(QString("can't get VariableType").toStdString());
            return false;
        }
        for (int j = 0; j < variables.size(); j++)
        {
            // 若数据一致则删除
            if (compareVariableType(variable_temp[i], variables[j]))
            {
                qx_query query;
                query.where("MainId").isEqualTo(QVariant::fromValue(variable_temp[i]->mainID));
                deleteTable_by_query<VariableType>(query);
                break;
            }
        }
    }

    // 将临时数据修改为正式数据
    QSqlQuery query(m_db);
    QString sql = "UPDATE VariableType SET type = 'EXTEND' WHERE type = 'EXTEND_TEMP'";
    if (!query.exec(sql))
    {
        LOG_ERROR_DEFAULT(QString("Failed to update VariableType").toStdString());
        return false;
    }

    return true;
}

bool VariableManage::readVariableType2File(const QString &fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        LOG_ERROR_DEFAULT(QString("Cannot open file for writing:1%").arg(fileName).toStdString());
        return false;
    }

    QTextStream out(&file);

    // 写入文件头部（[IEC_IEC_NORM]）
    out << "[IEC_IEC_NORM]" << endl;
    out << "\n";

    QVector<QSharedPointer<VariableType>> all_variable_types;
    get_variable_types("EXTEND", all_variable_types);
    for (int i = 0; i < all_variable_types.size(); i++)
    {
        out << "GLOBAL_TYPE_BEGIN" << endl;
        out << "TYPE" << endl;

        QVector<QSharedPointer<VariableType>> variables;
        QMap<QString, QVariant> map_device_variable_setting;
        map_device_variable_setting.insert("MainID", static_cast<int>(all_variable_types[i]->mainID));
        if (!get_Table_ByAQMap<QVector<QSharedPointer<VariableType>>>(variables, map_device_variable_setting))
        {
            LOG_ERROR_DEFAULT(
                QString("can't get VariableType by mainID:%1").arg(all_variable_types[i]->mainID).toStdString());
            return false;
        }
        int deep = 0;
        for (int j = 0; j < variables.size(); j++)
        {
            out << "\t";
            for (int z = 0; z < variables[j]->deep; z++)
            {
                out << "\t";
            }

            if (variables[j]->arrayCount > 1) // 数组
            {
                int z = 0;
                for (; z + j < variables.size(); z++)
                {
                    qDebug() << "z:" << z;
                    if (variables[j]->id != variables[z + j]->mainID)
                    {
                        break;
                    }
                }
                z = z - 1;
                if (z == 0)
                {
                    out << variables[j]->name << " : array [ 0 .. " << QString::number(variables[j]->arrayCount - 1)
                        << " ] of " << variables[j]->dataType << ";" << endl;
                }
                else
                {
                    out << variables[j]->name << " : array [ 0 .. " << QString::number(variables[j]->arrayCount - 1)
                        << " , 0 .. " << QString::number(z) << " ] of " << variables[j]->dataType << ";" << endl;
                    j = j + z;
                    continue;
                }
            }
            else // 非数组
            {
                // 深度如果变大
                if ((j + 1 < variables.size()) && (variables[j]->deep - variables[j + 1]->deep == -1))
                {
                    out << variables[j]->name << " : " << variables[j]->dataType << ";" << endl;
                    deep = variables[j]->deep + 1;
                }
                else if ((j + 1 < variables.size()) && (variables[j]->deep - variables[j + 1]->deep == 1)) // 深度变小
                {
                    for (int z = 0; z < deep; z++)
                    {
                        out << "\t";
                    }
                    out << "end_struct;" << endl;
                    deep = 0;
                }
                else
                {
                    out << variables[j]->name << " : " << variables[j]->dataType << endl;
                }
            }
        }
        if (deep > 0)
        {
            for (int z = 0; z < deep; z++)
            {
                out << "\t";
            }
            out << "end_struct;" << endl;
        }

        out << "END_TYPE" << endl;
        out << "GLOBAL_TYPE_END" << endl;
        out << "\n";
    }

    out << "[ENDE]" << endl;
    return true;
}

bool VariableManage::checkFBAndFUNCName(const QString &name)
{
    bool flag = false;

    qx_query query;
    query.where("FBName").isEqualTo(name).orderAsc("FBName", "SortNumber");
    QList<FBTemplate> fblist;
    QSqlError daoError = qx::dao::fetch_by_query(query, fblist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbtemplate:" + daoError.text().toStdString());
    }
    if (fblist.size() == 0)
        flag = true;

    return flag;
}

bool VariableManage::addFBTemplate(const QString &deviceName, const QString &owned, const QString &fbType,
                                   const QString &childType, const QString &fbName, const QString &description,
                                   const QString &version, const QString &cCode, const QString &oFile,
                                   const QString &aFile, const QString &soFile)
{
    // 查询重名功能块
    qx_query query;
    query.where("FBName").isEqualTo(fbName);
    long count = qx::dao::count<FBTemplate>(query, &m_db);
    if (count > 0)
    {
        LOG_ERROR_DEFAULT("addFB same name");
        return false;
    }
    // 功能块名只能由1到30个字母字母、数字、下划线组成
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(fbName))
    {
        LOG_ERROR_DEFAULT(fbName.toStdString() + " is invalid");
        return false;
    }
    FBTemplate newVar;
    newVar.SettingName = deviceName;
    newVar.Owned = owned;
    newVar.FBType = fbType;
    newVar.ChildType = childType;
    newVar.FBName = fbName;
    newVar.Description = description;
    newVar.Version = version;
    newVar.CCode = cCode;
    newVar.OFile = oFile;
    newVar.AFile = aFile;
    newVar.SOFile = soFile;

    QSqlError daoError = qx::dao::insert(newVar, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("insert FBTemplate:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

QString VariableManage::modifyFBTemplate(const long &id, const QString &deviceName, const QString &owned,
                                         const QString &fbType, const QString &childType, const QString &fbName,
                                         const QString &description, const QString &version, const QString &cCode,
                                         const QString &oFile, const QString &aFile, const QString &soFile)
{
    // 变量名只能由1到30个字母字母、数字、下划线组成
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(fbName))
    {
        LOG_ERROR_DEFAULT(fbName.toStdString() + " is invalid");
        return QString("变量名称不规范,变量名称只能由2到30个字母、数字、下划线组成!!!");
    }
    FBTemplate fb;
    fb.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(fb, &m_db);

    // 查询重名变量
    qx_query query;
    query.where("FBName").isEqualTo(fbName);
    FBTemplate currVarTemp;
    QSqlError daoErrorTemp = qx::dao::fetch_by_query(query, currVarTemp, &m_db);
    long count = qx::dao::count<FBTemplate>(query, &m_db);
    if (count > 0 && currVarTemp.id != id)
    {
        LOG_ERROR_DEFAULT("modify fb count > 0");
        return QString("变量名称重复!!!");
    }
    fb.SettingName = deviceName;
    fb.Owned = owned;
    fb.FBType = fbType;
    fb.ChildType = childType;
    fb.FBName = fbName;
    fb.Description = description;
    fb.Version = version;
    fb.CCode = cCode;
    fb.OFile = oFile;
    fb.AFile = aFile;
    fb.SOFile = soFile;

    daoError = qx::dao::update(fb, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update fbtemplate:" + daoError.text().toStdString());
        return QString("修改失败!!!");
    }
    return QString();
}

bool VariableManage::addFBPinTemplate(const QString &fbName, const QString &scope, const QString &pinName,
                                      int sortnumber, const QString &direction, const QString &dataType,
                                      const QString &initValue, const QString &defaultValue, int arraylength,
                                      const QString &description)
{
    // 查询重名功能块
    qx_query query;
    query.where("FBName").isEqualTo(fbName).and_("PinName").isEqualTo(pinName);
    long count = qx::dao::count<FBPinTemplate>(query, &m_db);
    if (count > 0)
    {
        LOG_ERROR_DEFAULT("addFB same name");
        return false;
    }
    // 功能块名只能由1到30个字母字母、数字、下划线组成
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(pinName))
    {
        LOG_ERROR_DEFAULT(pinName.toStdString() + " is invalid");
        return false;
    }
    FBPinTemplate newVar;
    newVar.FBName = fbName;
    newVar.Scope = scope;
    newVar.PinName = pinName;
    newVar.SortNumber = sortnumber;
    newVar.Direction = direction;
    newVar.DataType = dataType;
    newVar.InitValue = initValue;
    newVar.DefaultValue = defaultValue;
    newVar.ArrayLength = arraylength;
    newVar.Description = description;

    QSqlError daoError = qx::dao::insert(newVar, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("insert FBPinTemplate:" + daoError.text().toStdString());
        return false;
    }
    return true;
}

QString VariableManage::modifyFBPinTemplate(const long &id, const QString &fbName, const QString &scope,
                                            const QString &pinName, const QString &direction, const QString &dataType,
                                            const QString &initValue, const QString &defaultValue, int arraylength,
                                            const QString &description)
{
    // 变量名只能由1到30个字母字母、数字、下划线组成
    // QRegExp nameExpr("^[a-zA-Z][a-zA-Z0-9_]{1,29}$");
    if (!nameExpr.exactMatch(pinName))
    {
        LOG_ERROR_DEFAULT(pinName.toStdString() + " is invalid");
        return QString("变量名称不规范,变量名称只能由2到30个字母、数字、下划线组成!!!");
    }
    FBPinTemplate fbpin;
    fbpin.id = id;
    QSqlError daoError = qx::dao::fetch_by_id(fbpin, &m_db);

    // 查询重名变量
    qx_query query;
    query.where("FBName").isEqualTo(fbName).and_("PinName").isEqualTo(pinName);
    FBPinTemplate currVarTemp;
    QSqlError daoErrorTemp = qx::dao::fetch_by_query(query, currVarTemp, &m_db);
    long count = qx::dao::count<FBPinTemplate>(query, &m_db);
    if (count > 0 && currVarTemp.id != id)
    {
        LOG_ERROR_DEFAULT("modify fbpin count > 0");
        return QString("变量名称重复!!!");
    }
    fbpin.FBName = fbName;
    fbpin.Scope = scope;
    fbpin.PinName = pinName;
    fbpin.Direction = direction;
    fbpin.DataType = dataType;
    fbpin.InitValue = initValue;
    fbpin.DefaultValue = defaultValue;
    fbpin.ArrayLength = arraylength;
    fbpin.Description = description;

    daoError = qx::dao::update(fbpin, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("update fbpintemplate:" + daoError.text().toStdString());
        return QString("修改失败!!!");
    }
    return QString();
}

QList<FBPinTemplate> VariableManage::getFBPinTemplateFromFBName(const QString &fbName)
{
    qx_query query;
    query.where("FBName").isEqualTo(fbName).orderAsc("FBName", "SortNumber");
    QList<FBPinTemplate> pinlist;
    QSqlError daoError = qx::dao::fetch_by_query(query, pinlist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbpintemplate:" + daoError.text().toStdString());
    }

    return pinlist;
}

QList<FBPinTemplate> VariableManage::getFBPinTemplateWithoutStaticFromFBName(const QString &fbName)
{
    qx_query query;
    query.where("FBName").isEqualTo(fbName).and_("Scope").isNotEqualTo("static").orderAsc("FBName", "SortNumber");
    QList<FBPinTemplate> pinlist;
    QSqlError daoError = qx::dao::fetch_by_query(query, pinlist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbpintemplate:" + daoError.text().toStdString());
    }

    return pinlist;
}

QJsonArray VariableManage::getLibFunctionAndBlock(const QString &fbType)
{
    QJsonArray aryf;

    if (fbType.toUpper() == "ADVANCE")
    {
        // 补充自定义高级功能块
        QString exfbcPath = appDir + "/Settings/extend_fbc.xml";
        ExtendFBC fbc;
        fbc.fromXml(exfbcPath);
        for (int i = 0; i < fbc.componentList->componentList.size(); i++)
        {
            QSharedPointer<ExComponent> com = fbc.componentList->componentList[i];
            if (com->Enable)
            {
                QJsonObject fbobj;
                // 函数名
                fbobj["name"] = com->Name;
                // 功能块
                fbobj["type"] = com->ChildType;
                fbobj["childtype"] = "";
                fbobj["desc"] = "";
                fbobj["dir"] = "Advance";
                // 输入引脚
                int index = 2;
                QJsonArray inputs;
                for (auto ele : com->var->input->elementList)
                {
                    QJsonObject in;
                    in["id"] = index;
                    in["name"] = ele->Name;
                    in["type"] = ele->dataType;
                    in["direction"] = "Input";
                    in["initValue"] = "";
                    in["defaultValue"] = "";
                    in["arraylength"] = 1;
                    in["address"] = "";
                    in["description"] = "";
                    inputs.append(in);

                    index++;
                }
                fbobj["inputs"] = inputs;
                // 输出引脚
                QJsonArray outputs;
                for (auto ele : com->var->output->elementList)
                {
                    QJsonObject out;
                    out["id"] = index;
                    out["name"] = ele->Name;
                    out["type"] = ele->dataType;
                    out["direction"] = "Output";
                    out["initValue"] = "";
                    out["defaultValue"] = "";
                    out["arraylength"] = 1;
                    out["address"] = "";
                    out["description"] = "";
                    outputs.append(out);

                    index++;
                }
                fbobj["outputs"] = outputs;
                aryf.append(fbobj);
            }
        }
    }
    else
    {
        qx_query query;
        query.where("Owned")
            .isEqualTo("INSIDE")
            .and_("Enable")
            .isEqualTo(1)
            .and_("FBType")
            .isEqualTo(fbType.toUpper())
            .orderAsc("FBName");
        QList<FBTemplate> fblist;
        QSqlError daoError = qx::dao::fetch_by_query(query, fblist, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT("read fbtemplate:" + daoError.text().toStdString());
        }

        QVariantList namelist;
        for (auto fb : fblist)
        {
            namelist.append(fb.FBName);
        }

        query.clear();
        query.where("FBName").in(namelist).orderAsc("FBName", "SortNumber");
        QList<FBPinTemplate> pinlist;
        daoError = qx::dao::fetch_by_query(query, pinlist, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT("read fbpintemplate:" + daoError.text().toStdString());
        }

        for (auto fb : fblist)
        {
            QJsonObject fbobj;
            // 函数名
            fbobj["name"] = fb.FBName;
            // 功能块或函数
            fbobj["type"] = fb.FBType;
            fbobj["childtype"] = fb.ChildType;
            fbobj["desc"] = fb.Description;
            fbobj["dir"] = fb.FBType;
            // 检查该函数是否有定义任何变量与引脚,如果没有则跳过
            bool findvar = false;
            for (auto pin : pinlist)
            {
                if (pin.FBName == fb.FBName)
                {
                    findvar = true;
                    break;
                }
            }
            if (!findvar)
            {
                continue;
            }

            int index = 2;
            // 输入引脚
            QJsonArray inputs;
            for (auto pin : pinlist)
            {
                if (pin.FBName == fb.FBName && (pin.Direction == "Input" || pin.Direction == "InOut"))
                {
                    QJsonObject in;
                    in["id"] = index;
                    in["name"] = pin.PinName;
                    in["type"] = pin.DataType;
                    in["direction"] = pin.Direction;
                    in["initValue"] = pin.InitValue;
                    in["defaultValue"] = pin.DefaultValue;
                    in["arraylength"] = pin.ArrayLength;
                    in["address"] = "";
                    in["description"] = pin.Description;
                    inputs.append(in);

                    index++;
                }
            }
            fbobj["inputs"] = inputs;
            // 输出引脚
            QJsonArray outputs;
            for (auto pin : pinlist)
            {
                if (pin.FBName == fb.FBName && pin.Direction == "Output")
                {
                    QJsonObject out;
                    out["id"] = index;
                    out["name"] = pin.PinName;
                    out["type"] = pin.DataType;
                    out["direction"] = pin.Direction;
                    out["initValue"] = pin.InitValue;
                    out["defaultValue"] = pin.DefaultValue;
                    out["arraylength"] = pin.ArrayLength;
                    out["address"] = "";
                    out["description"] = pin.Description;
                    outputs.append(out);

                    index++;
                }
            }
            fbobj["outputs"] = outputs;
            aryf.append(fbobj);
        }
    }
    return aryf;
}

QList<FBTemplate> VariableManage::getLibFunctionAndBlockList()
{
    QList<FBTemplate> fblist;
    qx_query query;
    query.where("Owned").isEqualTo("INSIDE");

    QSqlError daoError = qx::dao::fetch_by_query(query, fblist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbtemplate:" + daoError.text().toStdString());
    }
    return fblist;
}

QList<KeywordList> VariableManage::getKeywordList()
{
    qx_query query;
    query.where("Type").isEqualTo("CCode").or_("Type").isEqualTo("STCode");
    QList<KeywordList> list;
    QSqlError daoError = qx::dao::fetch_by_query(query, list, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT(daoError.text().toStdString());
    }
    return list;
}

bool VariableManage::checkVariableNameSyntax(const QString &deviceName, const QString &varname)
{
    qDebug() << "checkVariableNameSyntax" << deviceName << varname;

    bool flag = true;
    // 第一步按结构体层级展开
    QStringList namelist = varname.split(".");

    // qDebug() << "checkVariableNameSyntax" << namelist;
    // 主变量名称
    VariableList mainVb;
    // 父变量类型
    VariableType parentVT;
    for (int i = 0; i < namelist.size(); i++)
    {
        // 判断是否符合数组语法
        QString &name = namelist[i];
        QRegExp nameExpr("\\[\\d+\\]");
        if (name.indexOf("[") >= 0)
        {
            qDebug() << "checkVariableNameSyntax name nameExpr.indexIn(name)" << name << nameExpr.indexIn(name);
            if (nameExpr.indexIn(name) < 0)
            {
                flag = false;
                break;
            }
            else
            {
                // 取出数组长度
                qDebug() << "checkVariableNameSyntax nameExpr.capturedTexts" << nameExpr.capturedTexts();
            }
        }
        name = name.split("[")[0];

        qDebug() << "checkVariableNameSyntax name" << name;
        if (i == 0)
        {
            // 主变量名称 去变量表内查找
            // qDebug() << "checkVariableNameSyntax name" << name;
            qx_query query;
            query.where("settingName").isEqualTo(deviceName);
            QList<VariableList> allVblist;
            QSqlError daoError = qx::dao::fetch_by_query(query, allVblist, &m_db);
            if (daoError.type() != QSqlError::NoError)
            {
                LOG_TRACE_DEFAULT("read variablelist:" + daoError.text().toStdString());
            }

            // 在应用层进行不区分大小写的名称过滤
            QList<VariableList> vblist;
            for (const auto &var : allVblist)
            {
                if (QString::compare(var.name, name, Qt::CaseInsensitive) == 0)
                {
                    vblist.append(var);
                }
            }

            qDebug() << "checkVariableNameSyntax vblist.size()" << vblist.size();
            if (vblist.size() >= 1)
            {
                // 判断数组长度是否符合
                mainVb = vblist[0];
                parentVT = getDataTypeByID(mainVb.dataTypeID);
            }
            else
            {
                flag = false;
                qDebug() << "checkVariableNameSyntax array failed";
                break;
            }
        }
        else
        {
            // 结构体展开属性名称 去结构体定义内查找 三种 基本类型 内部结构体 外部结构体类型
            QList<VariableType> childlist = getDataTypeChildListByParentID(parentVT.id);
            bool findone = false;
            for (auto child : childlist)
            {
                if (child.name == name)
                {
                    // 判断数组长度是否合适

                    // 判断类型是否是基础类型还是结构体
                    if (isBaseDataType(child.dataType))
                    {
                        // 基础型
                        parentVT = VariableType();
                    }
                    else if (child.dataType == "STRUCT")
                    {
                        // 内部结构体类型
                        parentVT = child;
                    }
                    else
                    {
                        // 外部结构体类型
                        parentVT = getSTRUCTDataTypeByName(deviceName, child.dataType);
                    }
                    findone = true;
                }
            }
            if (!findone)
            {
                flag = false;
                qDebug() << "checkVariableNameSyntax STRUCT failed";
                break;
            }
        }
    }

    qDebug() << "checkVariableNameSyntax" << flag;
    return flag;
}

QJsonObject VariableManage::getAllFunctionAndBlock()
{
    QJsonObject obj;

    // 系统
    obj["firmfunction"] = getLibFunctionAndBlock("FUNCTION");
    obj["firmfunctionblock"] = getLibFunctionAndBlock("FUNCTIONBLOCK");
    obj["advance"] = getLibFunctionAndBlock("ADVANCE");
    // 用户
    // obj["pptall"] = pttary;

    return obj;
}

QVector<QStringList> VariableManage::getAllLibFBVec()
{
    qx_query query;
    query.orderAsc("FBName", "SortNumber");
    QList<FBPinTemplate> pinlist;
    QSqlError daoError = qx::dao::fetch_by_query(query, pinlist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbpintemplate:" + daoError.text().toStdString());
    }

    query.clear();
    query.where("Owned").isEqualTo("INSIDE").and_("Enable").isGreaterThanOrEqualTo(1).orderAsc("FBName");
    QList<FBTemplate> fblist;
    daoError = qx::dao::fetch_by_query(query, fblist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbtemplate:" + daoError.text().toStdString());
    }

    QVector<QStringList> vec;
    //    for (auto fb : fblist)
    //    {
    //        //功能块的话添加一个Local USINT的本地变量
    //        if (fb.FBType == "FUNCTIONBLOCK")
    //        {
    //            QStringList list;
    //            list << QString::number(35000) << "" << "Local" << fb.Owned
    //                 << fb.FBName << fb.FBType << "_" + fb.FBName << "USINT" << "" << "1";
    //            vec.append(list);
    //        }

    //        for (auto pin : pinlist)
    //        {
    //            if (fb.FBName == pin.FBName)
    //            {
    //                QStringList list;
    //                list << QString::number(pin.id) << "" << pin.Scope << fb.Owned
    //                     << pin.FBName << fb.FBType << pin.PinName << pin.DataType << pin.InitValue << "1";
    //                vec.append(list);
    //            }
    //        }
    //    }

    if (!pinlist.isEmpty())
    {
        for (auto pin : pinlist)
        {
            QString owned = "";
            QString fbtype = "";
            bool findone = false;
            for (auto fb : fblist)
            {
                if (fb.FBName == pin.FBName)
                {
                    owned = fb.Owned;
                    fbtype = fb.FBType;
                    findone = true;
                    break;
                }
            }
            if (findone)
            {
                QStringList list;
                list << QString::number(pin.id) << "" << pin.Scope << owned << pin.FBName << fbtype << pin.PinName
                     << pin.DataType << pin.InitValue << QString::number(pin.ArrayLength) << "" << pin.Description;
                vec.append(list);
            }
        }
    }
    return vec;
}

QVector<QStringList> VariableManage::getAllLibFBCodeVec()
{
    qx_query query;
    query.where("Owned").isEqualTo("INSIDE").and_("Enable").isGreaterThanOrEqualTo(1).orderAsc("FBName");
    QList<FBTemplate> fblist;
    QSqlError daoError = qx::dao::fetch_by_query(query, fblist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbtemplate:" + daoError.text().toStdString());
    }

    QVector<QStringList> vec;
    if (!fblist.isEmpty())
    {
        for (auto fb : fblist)
        {
            QStringList list;
            list << QString::number(fb.id) << fb.FBName << fb.FBType << fb.CCode << fb.OFile << fb.AFile << fb.SOFile;
            vec.append(list);
        }
    }
    return vec;
}

QVector<QStringList> VariableManage::getAllLibDataTypeVec()
{
    qx_query query;
    query.where("Owned")
        .isEqualTo("INSIDE")
        .and_("Enable")
        .isGreaterThanOrEqualTo(1)
        .and_("FBType")
        .isEqualTo("FUNCTIONBLOCK")
        .orderAsc("FBName");
    QList<FBTemplate> fblist;
    QSqlError daoError = qx::dao::fetch_by_query(query, fblist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbtemplate:" + daoError.text().toStdString());
    }

    QVariantList vlist;
    for (auto fb : fblist)
    {
        vlist.append(fb.FBName);
    }

    query.clear();
    query.where("FBName")
        .in(vlist)
        .and_("Scope")
        .in("Local", "local", "Static", "static")
        .orderAsc("FBName", "SortNumber");
    QList<FBPinTemplate> pinlist;
    daoError = qx::dao::fetch_by_query(query, pinlist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_TRACE_DEFAULT("read fbpintemplate:" + daoError.text().toStdString());
    }
    // 记录每个主结构体的总长度
    QMap<QString, QString> mainOffsetMap;

    QVector<QStringList> vec;
    if (!pinlist.isEmpty())
    {
        int id = 40000;
        int mainid = 40000;
        QString fbName = "";
        int offset = 0;

        for (auto pin : pinlist)
        {
            if (pin.FBName != fbName)
            {
                // 记录每个结构体的全部长度信息
                mainOffsetMap.insert(fbName + "_INSIDE", QString::number(offset));

                // 开始新一组结构体的数据
                fbName = pin.FBName;
                mainid = id;
                offset = 0;
                // 添加主结构体
                QStringList mlist;
                mlist << QString::number(mainid) << "" << "USER" << "0" << fbName + "_INSIDE" << ""
                      << "STRUCT" << "1" << "0" << "bitlength" << "0" << QString::number(mainid) << "description"
                      << QString::number(offset);
                vec.append(mlist);
                id++;
                // 添加默认属性USINT
                //                 QStringList list;
                //                 list << QString::number(id) << "" << "ATTRIBUTE" << "0" << ("_" + fbName) << ""
                //                      << "USINT" << "1" << "1" << QString::number(8) << QString::number(mainid) <<
                //                      QString::number(
                //                                  mainid) << "description" << QString::number(offset);
                //                 vec.append(list);

                //                offset += 8;
                //                id++;
            }
            // 添加属性
            int bitlength = getBitLengthFromName("", pin.DataType);
            QStringList list;
            list << QString::number(id) << ""
                 << "ATTRIBUTE" << QString::number(pin.SortNumber) << pin.PinName << "" << pin.DataType << "1"
                 << "1" << QString::number(bitlength) << QString::number(mainid) << QString::number(mainid)
                 << "description" << QString::number(offset);
            vec.append(list);

            offset += bitlength;
            id++;
        }
        mainOffsetMap.insert(fbName + "_INSIDE", QString::number(offset));
    }
    // qDebug() << "getAllLibDataTypeVec" << mainOffsetMap;
    // 修改每个主结构体的总长度
    qDebug() << "vec" << vec;

    QVector<QStringList> newvec;
    for (auto row : vec)
    {
        // qDebug() << "getAllLibDataTypeVec" << row[2] << row[4] << row[9];
        if (row[2] == "USER")
        {
            if (mainOffsetMap.contains(row[4]))
            {
                row[9] = mainOffsetMap.value(row[4]);

                // qDebug() << "getAllLibDataTypeVec" << row[2] << row[4] << row[9];
            }
        }
        newvec.append(row);
    }
    // qDebug() << "getAllLibDataTypeVec" << newvec;
    return newvec;
}

void VariableManage::importLibFBFromCSV(QString codefileName, QString pinfileName)
{
    qDebug() << "importLibFBFromCSV";
    QFile codefile(appDir + "/Settings/" + codefileName);
    if (!codefile.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qDebug() << "importLibFBFromCSV codefile open failed!";
        return;
    }

    QTextStream stream(&codefile);
    stream.setCodec("UTF-8");
    QList<FBTemplate> codeData;
    while (!stream.atEnd())
    {
        QString line = stream.readLine();
        qDebug() << "line" << line;
        line = line.replace(",,,", "");
        // 对前五个逗号进行处理
        int index = 0;
        int findone = 0;
        while (findone < 5)
        {
            int findindex = line.indexOf(",", index);
            if (findindex > -1)
            {
                line.replace(findindex, 1, '$');

                findone++;
                index = findindex;

                // qDebug() << "importLibFBFromCSV line replace" << findindex << line << findone;
            }
        }
        QStringList row = line.split('$');

        qDebug() << "importLibFBFromCSV row" << row;

        if (row[0] != "id")
        {
            FBTemplate newCode;
            newCode.SettingName = "";
            newCode.Owned = "INSIDE";
            newCode.FBType = row[2];
            newCode.ChildType = row[3];
            newCode.FBName = row[1].trimmed();
            newCode.Description = row[4];
            newCode.Version = "1.0";
            // newCode.CCode = row[3];
            newCode.CCode = row[5].replace("\"\"", "####").trimmed();
            newCode.OFile = "";
            newCode.AFile = "";
            newCode.SOFile = "";
            if (newCode.FBName == "MEMCPY" || newCode.CCode == "" || newCode.FBType == "FUNCTIONBLOCK"
                //    || newCode.FBName.indexOf("STRING") > -1
            )
            {
                newCode.Enable = 0;
            }
            else if (newCode.FBName == "CONTACT" || newCode.FBName == "COIL" || newCode.FBName == "SET1" ||
                     newCode.FBName == "SET0")
            {
                newCode.Enable = 2;
            }
            else
            {
                newCode.Enable = 1;
            }

            codeData.append(newCode);
        }
    }
    codefile.close();

    QFile pinfile(appDir + "/Settings/" + pinfileName);
    if (!pinfile.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qDebug() << "importLibFBFromCSV pinfile open failed!";
        return;
    }

    QTextStream pinstream(&pinfile);
    QList<FBPinTemplate> pinData;
    while (!pinstream.atEnd())
    {
        QString line = pinstream.readLine();

        // qDebug() << "importLibFBFromCSV line" << line;
        QStringList row = line.split(',');
        // qDebug() << "importLibFBFromCSV row" << row;
        if (row[0] != "id")
        {
            FBPinTemplate newPin;
            newPin.FBName = row[4];
            newPin.Scope = row[2];
            newPin.PinName = row[6].trimmed();
            newPin.SortNumber = row[10].toInt();
            newPin.Direction = row[2];
            newPin.DataType = row[7];
            newPin.InitValue = row[8];
            newPin.DefaultValue = "";
            newPin.ArrayLength = 1;
            newPin.Description = "";

            pinData.append(newPin);
        }
    }
    codefile.close();
    qDebug() << "importLibFBFromCSV codeData.size() pinData.size()" << codeData.size() << pinData.size();
    if (codeData.size() > 0 && pinData.size() > 0)
    {
        for (auto &code : codeData)
        {
            // 查询其引脚
            QList<FBPinTemplate> pinList;
            for (auto &conn : pinData)
            {
                if (conn.FBName == code.FBName)
                {
                    pinList.append(conn);
                }
            }
            if (pinList.size() > 0)
            {
                // 排序引脚
                std::sort(pinList.begin(), pinList.end(),
                          [](FBPinTemplate &a, FBPinTemplate &b) { return a.SortNumber < b.SortNumber; });
                // 添加到主表
                QSqlError daoError = qx::dao::insert(code, &m_db);
                if (daoError.type() != QSqlError::NoError)
                {
                    qDebug() << "importLibFBFromCSV add FBTemplate:" << daoError.text();
                    return;
                }
                // 添加到子表
                for (auto &addconn : pinList)
                {
                    daoError = qx::dao::insert(addconn, &m_db);
                    if (daoError.type() != QSqlError::NoError)
                    {
                        qDebug() << "importLibFBFromCSV add FBPinTemplate:" << daoError.text();
                        return;
                    }
                }
            }
            else
            {
                qDebug() << "importLibFBFromCSV add FBTemplate pinList size 0" << code.FBName;
            }
        }
    }
}

int VariableManage::getBitLengthFromName(const QString &settingName, const QString &name)
{
    int length = 0;

    if (name != "STRUCT")
    {
        VariableType currType;
        qx_query query;
        query.where_OpenParenthesis("settingName")
            .isEqualTo(settingName)
            .or_("settingName")
            .isNull()
            .or_("settingName")
            .isEqualTo("")
            .closeParenthesis()
            .and_("deep")
            .isEqualTo(QVariant::fromValue(0))
            .and_("name")
            .isEqualTo(name);
        QSqlError daoError = qx::dao::fetch_by_query(query, currType, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_TRACE_DEFAULT("search VariableType:" + daoError.text().toStdString());
            return 0;
        }
        qDebug() << "currType.name" << currType.name << currType.bitLength << settingName << name;
        length = currType.bitLength;
    }

    return length;
}

template <typename T> bool VariableManage::deleteTable_by_query(qx_query &query)
{
    // 执行条件删除
    QSqlError daoError = qx::dao::delete_by_query<T>(query, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT(daoError.text().toStdString());
        return false;
    }
    return true;
}
