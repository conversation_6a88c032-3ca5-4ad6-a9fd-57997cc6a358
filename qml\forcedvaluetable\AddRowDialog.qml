import QtQuick 2.0
import QtQuick.Controls 2.12
import QtQuick.Dialogs 1.3

Dialog {
    id: dialog
    title: identifier ? "插入行" : "添加行"
    standardButtons: StandardButton.Ok | StandardButton.Cancel

    //添加前总数量
    property int rowCount

    //使用触发器监视枚举值
    property var selectedOptionText: "永久"

    //强制状态枚举值
    property var selectedStateText: "开启"

    //是否强制枚举值
    property bool selectedForceText: true

    //错误信息
    property string errorMessage

    // 用于接收标识的属性
    property bool identifier

    Column {
        spacing: 10

        // 在需要使用 rowCount 的地方进行展示或处理
        Text {
            text: "当前行数：" + rowCount
        }

        Row {
            visible: identifier
            Text {
                height: 30  // 设置高度
                text: "ID："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: idInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "变量索引："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: varIDInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "强制表名称："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: forceNameInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "监视值："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: monitoredValueInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "显示格式："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: displayFormatInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "使用触发器监视："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            ComboBox {
                id: monitoringWithTriggerInput
                model: ListModel {
                    id: triggerOptionsModel
                    ListElement {
                        text: "永久"
                    }
                    ListElement {
                        text: "暂时"
                    }
                    ListElement {
                        text: "不使用"
                    }
                }
                width: 200  // 设置宽度
                height: 30  // 设置高度
                textRole: "text" // 设置显示文本的角色
                onActivated: {
                    var selectedIndex = monitoringWithTriggerInput.currentIndex; // 获取当前选中项的索引
                    selectedOptionText = triggerOptionsModel.get(selectedIndex).text; // 获取选中项的文本值
                    //console.log("选中的选项文本值为：" + selectedOptionText); // 输出选中的选项文本值
                }
            }

        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "强制值："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: forcedValueInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "是否强制："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            ComboBox {
                id: isForcedInput
                model: ListModel {
                    id: forceModel
                    ListElement {
                        text: "true"
                    }
                    ListElement {
                        text: "false"
                    }
                }
                width: 200  // 设置宽度
                height: 30  // 设置高度
                textRole: "text" // 设置显示文本的角色
                onActivated: {
                    var selectedIndex = isForcedInput.currentIndex; // 获取当前选中项的索引
                    selectedForceText = JSON.parse(forceModel.get(selectedIndex).text); // 获取选中项的文本值
                    //console.log("选中的选项文本值为：" + selectedForceText); // 输出选中的选项文本值
                }
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "强制注释："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            TextField {
                id: descriptionInput
                placeholderText: "请输入"
                width: 200  // 设置宽度
                height: 30  // 设置高度
            }
        }

        Row {
            Text {
                height: 30  // 设置高度
                text: "强制状态："
                horizontalAlignment: Text.AlignHCenter // 水平居中对齐
                verticalAlignment: Text.AlignVCenter // 垂直居中对齐
            }

            ComboBox {
                id: stateInput
                model: ListModel {
                    id: stateModel
                    ListElement {
                        text: "开启"
                    }
                    ListElement {
                        text: "关闭"
                    }
                }
                width: 200  // 设置宽度
                height: 30  // 设置高度
                textRole: "text" // 设置显示文本的角色
                onActivated: {
                    var selectedIndex = stateInput.currentIndex; // 获取当前选中项的索引
                    selectedStateText = stateModel.get(selectedIndex).text; // 获取选中项的文本值
                    //console.log("选中的选项文本值为：" + selectedStateText); // 输出选中的选项文本值
                }
            }
        }
    }

    onAccepted: {
        //强制表名称不能为空
        if (forceNameInput.text === "") {
            errorMessage = "强制表名称不能为空"
            errorDialog.open()
            return
        }
        //ID不能为空
        if (identifier && idInput.text === "") {
            errorMessage = "ID不能为空"
            errorDialog.open()
            return
        }
        //id不能重复
        if (etablemodel.count > 0 || identifier){
            for (let i = 0; i < etablemodel.count; i++) {
                if (etablemodel.get(i).ID == parseInt(idInput.text)){
                    errorMessage = "ID不能重复"
                    errorDialog.open()
                    return
                }
            }
        }
        //获取最后一个
        const lastElementIndex = etablemodel.count - 1;
        var newData = {
            "ID": identifier ? parseInt(idInput.text) : getLastIndex(lastElementIndex) + 1,
            "varID":  parseInt(varIDInput.text),
            "forceName": forceNameInput.text,
            "monitoredValue": monitoredValueInput.text,
            "displayFormat": displayFormatInput.text,
            "monitoringWithTrigger": selectedOptionText,
            "forcedValue": forcedValueInput.text,
            "isForced": selectedForceText,
            "description": descriptionInput.text,
            "state": selectedStateText,
            selected: false
        };
        //添加操作
        etablemodel.append(newData);
    }

    function getLastIndex(lastElementIndex) {
        if (lastElementIndex !== -1) {
            const lastElement = etablemodel.get(lastElementIndex);
            return lastElement.ID
        }
    }

    //错误信息提示
    MessageDialog {
        id: errorDialog
        title: "Error"
        text: errorMessage
        visible: false
        standardButtons: StandardButton.Ok | StandardButton.Cancel
    }
}