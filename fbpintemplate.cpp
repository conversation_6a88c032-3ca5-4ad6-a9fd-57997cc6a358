﻿#include "fbpintemplate.h"
#include "QxOrm_Impl.h"

QX_REGISTER_CPP_EXPORT_DLL(FBPinTemplate)

namespace qx
{
template <>
void register_class(QxClass<FBPinTemplate> &t)
{
    t.id(&FBPinTemplate::id, "ID");
    t.data(&FBPinTemplate::FBName, "FBName");
    t.data(&FBPinTemplate::Scope, "Scope");
    t.data(&FBPinTemplate::PinName, "PinName");
    t.data(&FBPinTemplate::SortNumber, "SortNumber");
    t.data(&FBPinTemplate::Direction, "Direction");
    t.data(&FBPinTemplate::DataType, "DataType");
    t.data(&FBPinTemplate::InitValue, "InitValue");
    t.data(&FBPinTemplate::DefaultValue, "DefaultValue");
    t.data(&FBPinTemplate::ArrayLength, "ArrayLength");
    t.data(&FBPinTemplate::Description, "Description");
}
}
