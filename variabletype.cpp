﻿#include "variabletype.h"
#include "QxOrm_Impl.h"

QX_REGISTER_CPP_EXPORT_DLL(VariableType)

namespace qx
{
template <>
void register_class(QxClass<VariableType> &t)
{
    t.id(&VariableType::id, "ID");
    t.data(&VariableType::settingName, "SettingName");
    t.data(&VariableType::type, "Type");
    t.data(&VariableType::sortNumber, "SortNumber");
    t.data(&VariableType::name, "Name");
    t.data(&VariableType::shortName, "ShortName");
    t.data(&VariableType::dataType, "DataType");
    t.data(&VariableType::arrayCount, "ArrayCount");
    t.data(&VariableType::deep, "Deep");
    t.data(&VariableType::bitLength, "BitLength");
    t.data(&VariableType::parentID, "ParentID");
    t.data(&VariableType::mainID, "MainID");
    t.data(&VariableType::description, "Description");
    t.data(&VariableType::mainOffset, "MainOffset");
    t.data(&VariableType::iecNORM, "IECNORM");
    t.data(&VariableType::min, "Min");
    t.data(&VariableType::max, "Max");
    t.data(&VariableType::Initialvalue, "Initialvalue");
}
}
