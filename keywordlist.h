﻿#ifndef KEYWORDLIST_H
#define KEYWORDLIST_H
#include <QxOrm.h>
#include <QString>

class KeywordList
{
public:
    long id;             // ID
    QString SettingName; // 配置设备
    QString Name;        // 名称内容
    QString Type;      // 类型 STCode、CCode、Regular正则
    QString FunctionType;      // 功能类型 VariableName，FileName，DataTypeName，为空则都适用
};
QX_REGISTER_HPP_EXPORT_DLL(KeywordList, qx::trait::no_base_class_defined, 0)

#endif // KEYWORDLIST_H
