﻿#ifndef FORCEDVALUELIST_H
#define FORCEDVALUELIST_H

#include <QxOrm.h>
#include <QString>

class ForcedValueList
{
public:
    long id;                   // ID
    QString settingName;       // 配置名称
    QString tableName;         // 表名称
    long varID;                // 变量表变量索引
    QString forceName;         // 强制名称
    QString monitoredValue;    // 监视值
    QString displayFormat;     // 显示格式
    int monitoringWithTrigger; // 使用触发器监视
    QString forcedValue;       // 强制值
    int isForced;              // 是否强制
    QString description;       // 注释
    int state;                 // 状态
    QString scope;             // 变量作用域
    QString owned;             // 文件名
    QString type;              // 文件分类
    QString dataType;          // 数据类型
    long dataTypeID;           // 数据类型ID
    ForcedValueList() {};
};

QX_REGISTER_HPP_EXPORT_DLL(ForcedValueList, qx::trait::no_base_class_defined, 0)
#endif // FORCEDVALUELIST_H
