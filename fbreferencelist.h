#ifndef FBREFERENCELIST_H
#define FBREFERENCELIST_H
#include <QxOrm.h>
#include <QString>

class FBReferenceList
{
public:
    long id;                // ID
    QString settingName;    // 配置名称
    QString scope;          // 变量作用域
    QString owned;          // 文件名
    QString type;           // 文件分类
    QString referenceName;  // 引用的别名
    QString instanceName;   // 引用块原名称
    QString fbType;         // 功能或功能块
    QString createTime;     // 创建时间
    QString lastModifyTime; // 上一次修改时间
    FBReferenceList(){};
};
QX_REGISTER_HPP_EXPORT_DLL(FBReferenceList, qx::trait::no_base_class_defined, 0)

#endif // FBREFERENCELIST_H
